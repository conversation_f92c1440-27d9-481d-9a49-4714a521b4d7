<?xml version="1.0" encoding="UTF-8"?>
<Configuration>
  <!-- 
    增强版 Windows Sandbox 配置
    
    功能:
    - 自动配置代理
    - 自动安装常用应用
    - 预配置开发环境
    
    使用说明:
    1. 确保主机代理软件正在运行
    2. 确保防火墙允许7890端口
    3. 双击此文件启动沙盒
    4. 等待应用自动安装完成
  -->
  
  <!-- 映射主机文件夹 -->
  <MappedFolders>
    <MappedFolder>
      <HostFolder>E:\desk\ceshi</HostFolder>
      <SandboxFolder>C:\Users\<USER>\Desktop\HostFiles</SandboxFolder>
      <ReadOnly>false</ReadOnly>
    </MappedFolder>
  </MappedFolders>
  
  <!-- 启动时执行安装脚本 -->
  <LogonCommand>
    <Command>cmd.exe /c "C:\Users\<USER>\Desktop\HostFiles\install-apps.bat"</Command>
  </LogonCommand>
  
  <!-- 网络设置 -->
  <Networking>Enable</Networking>
  
  <!-- 性能设置 -->
  <VGpu>Enable</VGpu>
  <MemoryInMB>4096</MemoryInMB>
  
  <!-- 功能启用 -->
  <AudioInput>Enable</AudioInput>
  <VideoInput>Enable</VideoInput>
  <PrinterRedirection>Enable</PrinterRedirection>
  <ClipboardRedirection>Enable</ClipboardRedirection>
  
</Configuration>
