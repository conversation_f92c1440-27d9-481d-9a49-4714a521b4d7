<?xml version="1.0" encoding="UTF-8" ?>
<NotepadPlus>
		<AutoComplete>
			<KeyWord name="arguments" />
			<KeyWord name="and" />
			<KeyWord name="await" />
			<KeyWord name="break" />
			<KeyWord name="by" />
			<KeyWord name="catch" />
			<KeyWord name="class" />
			<KeyWord name="continue" />
			<KeyWord name="default" />
			<KeyWord name="defer" />
			<KeyWord name="delete" />
			<KeyWord name="do" />
			<KeyWord name="else" />
			<KeyWord name="extends" />
			<KeyWord name="false" />
			<KeyWord name="finally" />
			<KeyWord name="for" />
			<KeyWord name="if" />
			<KeyWord name="in" />
			<KeyWord name="is" />
			<KeyWord name="isnt" />
			<KeyWord name="Infinity" />
			<KeyWord name="instanceof" />
			<KeyWord name="loop" />
			<KeyWord name="new" />
			<KeyWord name="not" />
			<KeyWord name="null" />
			<KeyWord name="NaN" />
			<KeyWord name="of" />
			<KeyWord name="or" />
			<KeyWord name="on" />
			<KeyWord name="no" />
			<KeyWord name="return" />
			<KeyWord name="switch" />
			<KeyWord name="super" />
			<KeyWord name="then" />
			<KeyWord name="this" />
			<KeyWord name="throw" />
			<KeyWord name="true" />
			<KeyWord name="try" />
			<KeyWord name="typeof" />
			<KeyWord name="undefined" />
			<KeyWord name="unless" />
			<KeyWord name="when" />
			<KeyWord name="while" />
			<KeyWord name="yes" />
			<KeyWord name="yield" />
			<KeyWord name="=>" />
			<KeyWord name="->" />
		</AutoComplete>
</NotepadPlus>