# Windows Sandbox 便携应用系统使用说明

## 🚀 快速开始

### 方法1：一键启动 (推荐)
1. 双击 `启动沙盒.bat`
2. 选择对应的启动模式：
   - **增强模式** - 自动安装常用应用
   - **便携模式** - 使用预置便携应用
   - **自定义模式** - 选择性安装应用
   - **基础模式** - 仅配置代理

### 方法2：下载便携应用
1. 进入 `PortableApps` 文件夹
2. 运行 `简单下载器.bat` (推荐) 或 `下载常用应用.bat`
3. 等待下载完成
4. 手动解压应用到对应文件夹

## 📁 文件夹结构说明

```
PortableApps/
├── 📖 README.md                    # 总体说明文档
├── 📥 下载常用应用.bat              # 完整下载脚本 (可能有兼容性问题)
├── 📥 简单下载器.bat                # 简化下载脚本 (推荐使用)
├── 🌐 Browsers/                    # 浏览器
├── ✏️ Editors/                     # 编辑器
├── 💻 Development/                 # 开发工具
├── 🔧 System/                      # 系统工具
├── 🎵 Media/                       # 媒体工具
├── 🌐 Network/                     # 网络工具
├── 🔒 Security/                    # 安全工具
└── 🛠️ Utilities/                   # 实用工具
```

## 🔧 故障排除

### 问题1：下载脚本报错 "不是内部或外部命令"
**原因**：编码问题或PowerShell不可用
**解决方案**：
1. 使用 `简单下载器.bat` 代替 `下载常用应用.bat`
2. 确保系统支持curl命令
3. 手动下载应用

### 问题2：沙盒无法联网
**原因**：代理配置问题
**解决方案**：
1. 确保主机代理软件正在运行 (端口7890)
2. 检查防火墙设置
3. 手动配置代理：
   ```
   netsh interface portproxy add v4tov4 listenport=7890 listenaddress=0.0.0.0 connectport=7890 connectaddress=***********
   ```

### 问题3：应用无法运行
**原因**：缺少依赖或权限问题
**解决方案**：
1. 以管理员身份运行沙盒
2. 安装必要的运行库 (如 .NET Framework, Visual C++ Redistributable)
3. 检查应用是否需要特定配置

## 📋 推荐应用列表

### 🔥 必备应用
- **7-Zip** - 压缩解压工具
- **Notepad++** - 文本编辑器
- **Everything** - 文件搜索工具
- **Firefox/Chrome** - 网页浏览器

### 💻 开发工具
- **VS Code Portable** - 代码编辑器
- **Git Portable** - 版本控制
- **Python Portable** - Python环境
- **Node.js Portable** - JavaScript运行环境

### 🔧 系统工具
- **Process Monitor** - 进程监控
- **Process Explorer** - 任务管理器增强版
- **Autoruns** - 启动项管理
- **TCPView** - 网络连接查看

### 🎵 媒体工具
- **VLC Media Player** - 万能播放器
- **IrfanView** - 图片查看器
- **GIMP** - 图像编辑
- **Audacity** - 音频编辑

## 💡 使用技巧

1. **批量安装**：使用增强模式可以一次性安装多个常用应用
2. **自定义配置**：修改 `.wsb` 配置文件可以调整沙盒设置
3. **数据持久化**：将重要数据保存到映射的主机文件夹
4. **快捷方式**：便携应用会自动创建桌面快捷方式
5. **定期更新**：定期运行下载脚本更新应用版本

## 🔗 相关文件

- `启动沙盒.bat` - 主启动器
- `enhanced-sandbox.wsb` - 增强模式配置
- `proxy-sandbox.wsb` - 基础模式配置
- `install-apps.bat` - 应用安装脚本
- `custom-apps.bat` - 自定义安装脚本
- `portable-apps.bat` - 便携应用部署脚本

## 📞 支持

如果遇到问题：
1. 查看各文件夹中的 `说明.txt` 文件
2. 检查 Windows Sandbox 是否已启用
3. 确认网络连接和代理设置
4. 尝试以管理员身份运行脚本

---
*最后更新：2025年8月*
