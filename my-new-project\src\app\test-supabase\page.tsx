'use client'

import { useState, useEffect } from 'react'
import { supabase } from '@/lib/supabase'
import type { UserConfig } from '@/lib/supabase'

export default function TestSupabase() {
  const [userConfigs, setUserConfigs] = useState<UserConfig[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [connectionStatus, setConnectionStatus] = useState<string>('检查中...')

  useEffect(() => {
    testSupabaseConnection()
  }, [])

  const testSupabaseConnection = async () => {
    try {
      setLoading(true)
      setError(null)
      
      // 测试连接
      console.log('测试 Supabase 连接...')
      setConnectionStatus('正在连接...')
      
      // 获取用户配置数据
      const { data, error: fetchError } = await supabase
        .from('user_config')
        .select('*')
        .order('created_at', { ascending: false })

      if (fetchError) {
        throw fetchError
      }

      setUserConfigs(data || [])
      setConnectionStatus('✅ 连接成功')
      console.log('Supabase 数据:', data)
      
    } catch (err: any) {
      console.error('Supabase 连接错误:', err)
      setError(err.message)
      setConnectionStatus('❌ 连接失败')
    } finally {
      setLoading(false)
    }
  }

  const updateUserPoints = async () => {
    try {
      // 随机选择一个现有用户并更新其积分
      if (userConfigs.length === 0) {
        setError('没有用户数据可以更新')
        return
      }

      const randomUser = userConfigs[Math.floor(Math.random() * userConfigs.length)]
      const newPoints = Math.floor(Math.random() * 1000000)

      const { data, error } = await supabase
        .from('user_config')
        .update({
          points: newPoints,
          updated_at: new Date().toISOString()
        })
        .eq('id', randomUser.id)
        .select()

      if (error) throw error

      console.log('用户积分更新成功:', data)
      setError(null)
      // 重新获取数据
      testSupabaseConnection()
    } catch (err: any) {
      console.error('更新用户积分失败:', err)
      setError(err.message)
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 to-blue-100 p-8">
      <div className="max-w-6xl mx-auto">
        <div className="bg-white rounded-lg shadow-lg p-6">
          <div className="flex items-center justify-between mb-6">
            <h1 className="text-3xl font-bold text-gray-900">
              🧪 Supabase 连接测试
            </h1>
            <a
              href="/"
              className="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded transition-colors duration-300"
            >
              ← 返回首页
            </a>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
            <div className="bg-gray-50 p-4 rounded-lg">
              <h2 className="text-lg font-semibold mb-2 flex items-center">
                <span className="mr-2">🔗</span>
                连接状态
              </h2>
              <p className="text-lg font-medium">{connectionStatus}</p>
            </div>

            <div className="bg-gray-50 p-4 rounded-lg">
              <h2 className="text-lg font-semibold mb-2 flex items-center">
                <span className="mr-2">📊</span>
                数据统计
              </h2>
              <p className="text-lg font-medium">
                {userConfigs.length} 条记录
              </p>
            </div>
          </div>

          {error && (
            <div className="bg-red-100 border-l-4 border-red-500 text-red-700 px-4 py-3 rounded mb-6 animate-fade-in">
              <div className="flex items-center">
                <span className="text-xl mr-2">⚠️</span>
                <div>
                  <strong>错误:</strong> {error}
                </div>
              </div>
            </div>
          )}

          <div className="mb-6">
            <div className="flex flex-wrap gap-4 mb-4">
              <button
                onClick={testSupabaseConnection}
                disabled={loading}
                className="bg-blue-500 hover:bg-blue-700 text-white font-bold py-3 px-6 rounded-lg disabled:opacity-50 transition-all duration-300 shadow-md hover:shadow-lg flex items-center"
              >
                {loading ? (
                  <>
                    <div className="loading-spinner mr-2"></div>
                    测试中...
                  </>
                ) : (
                  <>
                    <span className="mr-2">🔄</span>
                    重新测试连接
                  </>
                )}
              </button>

              <button
                onClick={updateUserPoints}
                disabled={loading || userConfigs.length === 0}
                className="bg-green-500 hover:bg-green-700 text-white font-bold py-3 px-6 rounded-lg disabled:opacity-50 transition-all duration-300 shadow-md hover:shadow-lg flex items-center"
              >
                <span className="mr-2">💰</span>
                更新用户积分
              </button>

              <button
                onClick={() => setError(null)}
                className="bg-gray-500 hover:bg-gray-700 text-white font-bold py-3 px-6 rounded-lg transition-all duration-300 shadow-md hover:shadow-lg flex items-center"
              >
                <span className="mr-2">🧹</span>
                清除错误
              </button>
            </div>
          </div>

          <div>
            <h2 className="text-xl font-semibold mb-4 flex items-center">
              <span className="mr-2">📋</span>
              用户配置数据 ({userConfigs.length} 条记录)
            </h2>

            {loading ? (
              <div className="text-center py-12">
                <div className="loading-spinner mx-auto mb-4" style={{width: '48px', height: '48px'}}></div>
                <p className="text-gray-600">正在加载数据...</p>
              </div>
            ) : userConfigs.length === 0 ? (
              <div className="text-center py-12 bg-gray-50 rounded-lg">
                <div className="text-6xl mb-4">📭</div>
                <p className="text-gray-600 text-lg">暂无数据</p>
                <p className="text-gray-500 text-sm mt-2">点击"重新测试连接"来获取数据</p>
              </div>
            ) : (
              <div className="overflow-x-auto bg-white rounded-lg shadow">
                <table className="min-w-full">
                  <thead className="bg-gradient-to-r from-blue-50 to-indigo-50">
                    <tr>
                      <th className="px-6 py-3 border-b text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        ID
                      </th>
                      <th className="px-6 py-3 border-b text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        用户ID
                      </th>
                      <th className="px-6 py-3 border-b text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        积分
                      </th>
                      <th className="px-6 py-3 border-b text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        创建时间
                      </th>
                      <th className="px-6 py-3 border-b text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        更新时间
                      </th>
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-gray-200">
                    {userConfigs.map((config, index) => (
                      <tr
                        key={config.id}
                        className={`hover:bg-gray-50 transition-colors duration-200 ${
                          index % 2 === 0 ? 'bg-white' : 'bg-gray-50'
                        }`}
                      >
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex items-center">
                            <span className="text-sm font-mono text-gray-900 bg-gray-100 px-2 py-1 rounded">
                              {config.id.substring(0, 8)}...
                            </span>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className="text-sm font-mono text-gray-900 bg-blue-100 px-2 py-1 rounded">
                            {config.user_id.substring(0, 8)}...
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className="text-lg font-bold text-green-600">
                            💰 {config.points.toLocaleString()}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          📅 {new Date(config.created_at).toLocaleString('zh-CN')}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          🔄 {new Date(config.updated_at).toLocaleString('zh-CN')}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
