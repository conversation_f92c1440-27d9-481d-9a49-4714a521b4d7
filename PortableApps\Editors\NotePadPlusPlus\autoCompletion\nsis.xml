<?xml version="1.0" encoding="UTF-8" ?>
<NotepadPlus>
	<AutoComplete>
		<KeyWord name="!addincludedir" />
		<KeyWord name="!addplugindir" />
		<KeyWord name="!appendfile" />
		<KeyWord name="!assert" />
		<KeyWord name="!cd" />
		<KeyWord name="!define" />
		<KeyWord name="!delfile" />
		<KeyWord name="!echo" />
		<KeyWord name="!else" />
		<KeyWord name="!endif" />
		<KeyWord name="!error" />
		<KeyWord name="!execute" />
		<KeyWord name="!finalize" />
		<KeyWord name="!getdllversion" />
		<KeyWord name="!gettlbversion" />
		<KeyWord name="!if" />
		<KeyWord name="!ifdef" />
		<KeyWord name="!ifmacrodef" />
		<KeyWord name="!ifmacrondef" />
		<KeyWord name="!ifndef" />
		<KeyWord name="!include" />
		<KeyWord name="!insertmacro" />
		<KeyWord name="!macro" />
		<KeyWord name="!macroend" />
		<KeyWord name="!macroundef" />
		<KeyWord name="!makensis" />
		<KeyWord name="!packhdr" />
		<KeyWord name="!pragma" />
		<KeyWord name="!searchparse" />
		<KeyWord name="!searchreplace" />
		<KeyWord name="!system" />
		<KeyWord name="!tempfile" />
		<KeyWord name="!undef" />
		<KeyWord name="!uninstfinalize" />
		<KeyWord name="!verbose" />
		<KeyWord name="!warning" />
		<KeyWord name="Abort" />
		<KeyWord name="AddBrandingImage" />
		<KeyWord name="AddSize" />
		<KeyWord name="AllowRootDirInstall" />
		<KeyWord name="AllowSkipFiles" />
		<KeyWord name="AutoCloseWindow" />
		<KeyWord name="BGFont" />
		<KeyWord name="BGGradient" />
		<KeyWord name="BrandingText" />
		<KeyWord name="BringToFront" />
		<KeyWord name="Call" />
		<KeyWord name="CallInstDLL" />
		<KeyWord name="Caption" />
		<KeyWord name="ChangeUI" />
		<KeyWord name="CheckBitmap" />
		<KeyWord name="ClearErrors" />
		<KeyWord name="CompletedText" />
		<KeyWord name="ComponentText" />
		<KeyWord name="CopyFiles" />
		<KeyWord name="CPU" />
		<KeyWord name="CRCCheck" />
		<KeyWord name="CreateDirectory" />
		<KeyWord name="CreateFont" />
		<KeyWord name="CreateShortcut" />
		<KeyWord name="Delete" />
		<KeyWord name="DeleteINISec" />
		<KeyWord name="DeleteINIStr" />
		<KeyWord name="DeleteRegKey" />
		<KeyWord name="DeleteRegValue" />
		<KeyWord name="DetailPrint" />
		<KeyWord name="DetailsButtonText" />
		<KeyWord name="DirText" />
		<KeyWord name="DirVar" />
		<KeyWord name="DirVerify" />
		<KeyWord name="EnableWindow" />
		<KeyWord name="EnumRegKey" />
		<KeyWord name="EnumRegValue" />
		<KeyWord name="Exch" />
		<KeyWord name="Exec" />
		<KeyWord name="ExecShell" />
		<KeyWord name="ExecShellWait" />
		<KeyWord name="ExecWait" />
		<KeyWord name="ExpandEnvStrings" />
		<KeyWord name="File" />
		<KeyWord name="FileBufSize" />
		<KeyWord name="FileClose" />
		<KeyWord name="FileErrorText" />
		<KeyWord name="FileOpen" />
		<KeyWord name="FileRead" />
		<KeyWord name="FileReadByte" />
		<KeyWord name="FileReadUTF16LE" />
		<KeyWord name="FileReadWord" />
		<KeyWord name="FileSeek" />
		<KeyWord name="FileWrite" />
		<KeyWord name="FileWriteByte" />
		<KeyWord name="FileWriteUTF16LE" />
		<KeyWord name="FileWriteWord" />
		<KeyWord name="FindClose" />
		<KeyWord name="FindFirst" />
		<KeyWord name="FindNext" />
		<KeyWord name="FindWindow" />
		<KeyWord name="FlushINI" />
		<KeyWord name="Function" />
		<KeyWord name="FunctionEnd" />
		<KeyWord name="GetCurInstType" />
		<KeyWord name="GetCurrentAddress" />
		<KeyWord name="GetDlgItem" />
		<KeyWord name="GetDLLVersion" />
		<KeyWord name="GetDLLVersionLocal" />
		<KeyWord name="GetErrorLevel" />
		<KeyWord name="GetFileTime" />
		<KeyWord name="GetFileTimeLocal" />
		<KeyWord name="GetFullPathName" />
		<KeyWord name="GetFunctionAddress" />
		<KeyWord name="GetInstDirError" />
		<KeyWord name="GetKnownFolderPath" />
		<KeyWord name="GetLabelAddress" />
		<KeyWord name="GetRegView" />
		<KeyWord name="GetShellVarContext" />
		<KeyWord name="GetTempFileName" />
		<KeyWord name="GetWinVer" />
		<KeyWord name="Goto" />
		<KeyWord name="HideWindow" />
		<KeyWord name="Icon" />
		<KeyWord name="IfAbort" />
		<KeyWord name="IfAltRegView" />
		<KeyWord name="IfErrors" />
		<KeyWord name="IfFileExists" />
		<KeyWord name="IfRebootFlag" />
		<KeyWord name="IfRtlLanguage" />
		<KeyWord name="IfShellVarContextAll" />
		<KeyWord name="IfSilent" />
		<KeyWord name="InitPluginsDir" />
		<KeyWord name="InstallButtonText" />
		<KeyWord name="InstallColors" />
		<KeyWord name="InstallDir" />
		<KeyWord name="InstallDirRegKey" />
		<KeyWord name="InstProgressFlags" />
		<KeyWord name="InstType" />
		<KeyWord name="InstTypeGetText" />
		<KeyWord name="InstTypeSetText" />
		<KeyWord name="Int64Cmp" />
		<KeyWord name="Int64CmpU" />
		<KeyWord name="Int64Fmt" />
		<KeyWord name="IntCmp" />
		<KeyWord name="IntCmpU" />
		<KeyWord name="IntFmt" />
		<KeyWord name="IntOp" />
		<KeyWord name="IntPtrCmp" />
		<KeyWord name="IntPtrCmpU" />
		<KeyWord name="IntPtrOp" />
		<KeyWord name="IsWindow" />
		<KeyWord name="LangString" />
		<KeyWord name="LangStringUP" />
		<KeyWord name="LicenseBkColor" />
		<KeyWord name="LicenseData" />
		<KeyWord name="LicenseForceSelection" />
		<KeyWord name="LicenseLangString" />
		<KeyWord name="LicenseText" />
		<KeyWord name="LoadAndSetImage" />
		<KeyWord name="LoadLanguageFile" />
		<KeyWord name="LockWindow" />
		<KeyWord name="LogSet" />
		<KeyWord name="LogText" />
		<KeyWord name="ManifestAppendCustomString" />
		<KeyWord name="ManifestDisableWindowFiltering" />
		<KeyWord name="ManifestDPIAware" />
		<KeyWord name="ManifestDPIAwareness" />
		<KeyWord name="ManifestGdiScaling" />
		<KeyWord name="ManifestLongPathAware" />
		<KeyWord name="ManifestMaxVersionTested" />
		<KeyWord name="ManifestSupportedOS" />
		<KeyWord name="MessageBox" />
		<KeyWord name="MiscButtonText" />
		<KeyWord name="Name" />
		<KeyWord name="Nop" />
		<KeyWord name="OutFile" />
		<KeyWord name="Page" />
		<KeyWord name="PageCallbacks" />
		<KeyWord name="PageEx" />
		<KeyWord name="PageExEnd" />
		<KeyWord name="PEAddResource" />
		<KeyWord name="PEDllCharacteristics" />
		<KeyWord name="PERemoveResource" />
		<KeyWord name="PESubsysVer" />
		<KeyWord name="Pop" />
		<KeyWord name="Push" />
		<KeyWord name="Quit" />
		<KeyWord name="ReadEnvStr" />
		<KeyWord name="ReadINIStr" />
		<KeyWord name="ReadMemory" />
		<KeyWord name="ReadRegDWORD" />
		<KeyWord name="ReadRegStr" />
		<KeyWord name="Reboot" />
		<KeyWord name="RegDLL" />
		<KeyWord name="Rename" />
		<KeyWord name="RequestExecutionLevel" />
		<KeyWord name="ReserveFile" />
		<KeyWord name="Return" />
		<KeyWord name="RMDir" />
		<KeyWord name="SearchPath" />
		<KeyWord name="Section" />
		<KeyWord name="SectionEnd" />
		<KeyWord name="SectionGetFlags" />
		<KeyWord name="SectionGetInstTypes" />
		<KeyWord name="SectionGetSize" />
		<KeyWord name="SectionGetText" />
		<KeyWord name="SectionGroup" />
		<KeyWord name="SectionGroupEnd" />
		<KeyWord name="SectionIn" />
		<KeyWord name="SectionInstType" />
		<KeyWord name="SectionSetFlags" />
		<KeyWord name="SectionSetInstTypes" />
		<KeyWord name="SectionSetSize" />
		<KeyWord name="SectionSetText" />
		<KeyWord name="SendMessage" />
		<KeyWord name="SetAutoClose" />
		<KeyWord name="SetBrandingImage" />
		<KeyWord name="SetCompress" />
		<KeyWord name="SetCompressionLevel" />
		<KeyWord name="SetCompressor" />
		<KeyWord name="SetCompressorDictSize" />
		<KeyWord name="SetCtlColors" />
		<KeyWord name="SetCurInstType" />
		<KeyWord name="SetDatablockOptimize" />
		<KeyWord name="SetDateSave" />
		<KeyWord name="SetDetailsPrint" />
		<KeyWord name="SetDetailsView" />
		<KeyWord name="SetErrorLevel" />
		<KeyWord name="SetErrors" />
		<KeyWord name="SetFileAttributes" />
		<KeyWord name="SetFont" />
		<KeyWord name="SetOutPath" />
		<KeyWord name="SetOverwrite" />
		<KeyWord name="SetPluginUnload" />
		<KeyWord name="SetRebootFlag" />
		<KeyWord name="SetRegView" />
		<KeyWord name="SetShellVarContext" />
		<KeyWord name="SetSilent" />
		<KeyWord name="ShowInstDetails" />
		<KeyWord name="ShowUninstDetails" />
		<KeyWord name="ShowWindow" />
		<KeyWord name="SilentInstall" />
		<KeyWord name="SilentUnInstall" />
		<KeyWord name="Sleep" />
		<KeyWord name="SpaceTexts" />
		<KeyWord name="StrCmp" />
		<KeyWord name="StrCmpS" />
		<KeyWord name="StrCpy" />
		<KeyWord name="StrLen" />
		<KeyWord name="SubCaption" />
		<KeyWord name="SubSection" />
		<KeyWord name="SubSectionEnd" />
		<KeyWord name="Target" />
		<KeyWord name="Unicode" />
		<KeyWord name="UninstallButtonText" />
		<KeyWord name="UninstallCaption" />
		<KeyWord name="UninstallExeName" />
		<KeyWord name="UninstallIcon" />
		<KeyWord name="UninstallSubCaption" />
		<KeyWord name="UninstallText" />
		<KeyWord name="UninstPage" />
		<KeyWord name="UnRegDLL" />
		<KeyWord name="UnsafeStrCpy" />
		<KeyWord name="Var" />
		<KeyWord name="VIAddVersionKey" />
		<KeyWord name="VIFileVersion" />
		<KeyWord name="VIProductVersion" />
		<KeyWord name="WindowIcon" />
		<KeyWord name="WriteINIStr" />
		<KeyWord name="WriteRegBin" />
		<KeyWord name="WriteRegDWORD" />
		<KeyWord name="WriteRegExpandStr" />
		<KeyWord name="WriteRegMultiStr" />
		<KeyWord name="WriteRegNone" />
		<KeyWord name="WriteRegStr" />
		<KeyWord name="WriteUninstaller" />
		<KeyWord name="XPStyle" />
	</AutoComplete>
</NotepadPlus>
