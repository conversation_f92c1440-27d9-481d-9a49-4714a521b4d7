@echo off
title Windows Sandbox Proxy Setup
echo ========================================
echo Windows Sandbox 代理配置脚本
echo ========================================
echo.

echo [1/3] 检测网络配置...

REM 使用更简单的方法获取网关
ipconfig | findstr "默认网关" > temp_gateway.txt
if not exist temp_gateway.txt (
    ipconfig | findstr "Default Gateway" > temp_gateway.txt
)

set gateway=
for /f "tokens=*" %%i in (temp_gateway.txt) do (
    for /f "tokens=2 delims=:" %%j in ("%%i") do (
        set gateway=%%j
    )
)

REM 清理临时文件
if exist temp_gateway.txt del temp_gateway.txt

REM 去除空格
set gateway=%gateway: =%

if "%gateway%"=="" (
    echo 使用备用方法检测网关...
    for /f "tokens=3" %%i in ('route print ^| findstr "0.0.0.0"') do (
        if not "%%i"=="0.0.0.0" (
            set gateway=%%i
            goto :found_gateway
        )
    )
)

:found_gateway
if "%gateway%"=="" (
    echo 错误: 无法检测到默认网关
    echo 请手动设置网关地址
    set /p gateway=请输入网关地址:
)

echo 网关地址: %gateway%
echo.

echo [2/3] 配置端口代理...

REM 清除旧规则
netsh interface portproxy delete v4tov4 listenaddress=127.0.0.1 listenport=7890 2>nul

REM 添加新规则
netsh interface portproxy add v4tov4 listenaddress=127.0.0.1 listenport=7890 connectaddress=%gateway% connectport=7890

echo 端口代理配置完成
echo.

echo [3/3] 配置系统代理...

REM 设置IE代理（影响系统代理）
reg add "HKCU\Software\Microsoft\Windows\CurrentVersion\Internet Settings" /v ProxyEnable /t REG_DWORD /d 1 /f 2>nul
reg add "HKCU\Software\Microsoft\Windows\CurrentVersion\Internet Settings" /v ProxyServer /t REG_SZ /d "127.0.0.1:7890" /f 2>nul

echo 系统代理配置完成
echo.

echo ========================================
echo 配置完成!
echo ========================================
echo.
echo 代理信息:
echo - 本地代理: 127.0.0.1:7890
echo - 网关地址: %gateway%
echo.
echo 验证端口代理:
netsh interface portproxy show all
echo.
echo 现在可以使用浏览器测试网络连接
echo 建议访问: https://www.google.com
echo.
pause
