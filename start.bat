@echo off
echo ========================================
echo Windows Sandbox 代理配置脚本
echo ========================================
echo.

echo [1/4] 正在检测网络配置...

REM 获取默认网关IP地址
for /f "tokens=3" %%i in ('route print ^| findstr "0.0.0.0.*0.0.0.0"') do set gateway=%%i

if "%gateway%"=="" (
    echo 错误: 无法检测到默认网关
    echo 请检查网络连接
    pause
    exit /b 1
)

echo 检测到网关地址: %gateway%
echo.

echo [2/4] 正在配置端口代理...

REM 清除可能存在的旧规则
netsh interface portproxy delete v4tov4 listenaddress=127.0.0.1 listenport=7890 >nul 2>&1

REM 添加端口代理，将沙盒内的7890端口转发到主机的7890端口
netsh interface portproxy add v4tov4 listenaddress=127.0.0.1 listenport=7890 connectaddress=%gateway% connectport=7890

if %errorlevel% equ 0 (
    echo ✓ 端口代理配置成功
) else (
    echo ✗ 端口代理配置失败
    echo 请确保以管理员权限运行
    pause
    exit /b 1
)
echo.

echo [3/4] 正在配置系统代理...

REM 设置系统代理
reg add "HKCU\Software\Microsoft\Windows\CurrentVersion\Internet Settings" /v ProxyEnable /t REG_DWORD /d 1 /f >nul 2>&1
reg add "HKCU\Software\Microsoft\Windows\CurrentVersion\Internet Settings" /v ProxyServer /t REG_SZ /d "127.0.0.1:7890" /f >nul 2>&1

if %errorlevel% equ 0 (
    echo ✓ 系统代理配置成功
) else (
    echo ✗ 系统代理配置失败
)
echo.

echo [4/4] 验证配置...

REM 显示当前端口代理规则
echo 当前端口代理规则:
netsh interface portproxy show all
echo.

echo ========================================
echo 配置完成!
echo ========================================
echo.
echo 代理设置信息:
echo - 代理地址: 127.0.0.1:7890
echo - 网关地址: %gateway%
echo.
echo 使用说明:
echo 1. 浏览器代理已自动配置
echo 2. 如需手动设置，请使用: 127.0.0.1:7890
echo 3. 请确保主机代理软件正在运行
echo.
echo 测试建议:
echo - 访问 https://whatismyipaddress.com/ 检查IP
echo - 访问 https://www.google.com 测试连接
echo.

pause
