'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { supabase } from '@/lib/supabase'

export default function Home() {
  const [userCount, setUserCount] = useState<number>(0)
  const [totalPoints, setTotalPoints] = useState<number>(0)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    fetchStats()
  }, [])

  const fetchStats = async () => {
    try {
      const { data, error } = await supabase
        .from('user_config')
        .select('points')

      if (error) throw error

      setUserCount(data?.length || 0)
      setTotalPoints(data?.reduce((sum, user) => sum + user.points, 0) || 0)
    } catch (error) {
      console.error('获取统计数据失败:', error)
    } finally {
      setLoading(false)
    }
  }

  return (
    <main className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      <div className="container mx-auto px-4 py-16">
        <div className="text-center">
          <h1 className="text-4xl font-bold text-gray-900 mb-6 animate-fade-in">
            Welcome to My New Project
          </h1>
          <p className="text-xl text-gray-600 mb-8">
            A modern Next.js application with Supabase integration
          </p>

          {/* 统计信息卡片 */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 max-w-2xl mx-auto mb-8">
            <div className="bg-white p-4 rounded-lg shadow-md border-l-4 border-blue-500">
              <h3 className="text-lg font-semibold text-gray-800">用户数量</h3>
              <p className="text-2xl font-bold text-blue-600">
                {loading ? '...' : userCount}
              </p>
            </div>
            <div className="bg-white p-4 rounded-lg shadow-md border-l-4 border-green-500">
              <h3 className="text-lg font-semibold text-gray-800">总积分</h3>
              <p className="text-2xl font-bold text-green-600">
                {loading ? '...' : totalPoints.toLocaleString()}
              </p>
            </div>
          </div>

          {/* 技术栈卡片 */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-4xl mx-auto mb-8">
            <div className="bg-white p-6 rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300">
              <div className="text-3xl mb-3">⚡</div>
              <h2 className="text-xl font-semibold text-gray-800 mb-3">
                Next.js 15
              </h2>
              <p className="text-gray-600">
                Latest version with App Router and Turbopack
              </p>
            </div>
            <div className="bg-white p-6 rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300">
              <div className="text-3xl mb-3">⚛️</div>
              <h2 className="text-xl font-semibold text-gray-800 mb-3">
                React 19
              </h2>
              <p className="text-gray-600">
                Modern React with latest features
              </p>
            </div>
            <div className="bg-white p-6 rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300">
              <div className="text-3xl mb-3">🗄️</div>
              <h2 className="text-xl font-semibold text-gray-800 mb-3">
                Supabase
              </h2>
              <p className="text-gray-600">
                Backend-as-a-Service with authentication
              </p>
            </div>
          </div>

          {/* 操作按钮 */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              href="/test-supabase"
              className="bg-blue-500 hover:bg-blue-700 text-white font-bold py-3 px-6 rounded-lg transition-colors duration-300 shadow-md hover:shadow-lg"
            >
              🧪 测试 Supabase 连接
            </Link>
            <button
              onClick={fetchStats}
              disabled={loading}
              className="bg-green-500 hover:bg-green-700 text-white font-bold py-3 px-6 rounded-lg transition-colors duration-300 shadow-md hover:shadow-lg disabled:opacity-50"
            >
              {loading ? '刷新中...' : '🔄 刷新统计'}
            </button>
          </div>
        </div>
      </div>
    </main>
  )
}
