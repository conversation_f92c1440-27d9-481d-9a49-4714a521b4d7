@echo off
title 自定义应用安装器
echo ========================================
echo Windows Sandbox 自定义应用安装
echo ========================================
echo.

REM 配置代理
for /f "tokens=3" %%i in ('route print ^| findstr "0.0.0.0"') do (
    if not "%%i"=="0.0.0.0" (
        set gateway=%%i
        goto :found_gateway
    )
)
:found_gateway
if not "%gateway%"=="" (
    netsh interface portproxy add v4tov4 listenaddress=127.0.0.1 listenport=7890 connectaddress=%gateway% connectport=7890
    reg add "HKCU\Software\Microsoft\Windows\CurrentVersion\Internet Settings" /v ProxyEnable /t REG_DWORD /d 1 /f 2>nul
    reg add "HKCU\Software\Microsoft\Windows\CurrentVersion\Internet Settings" /v ProxyServer /t REG_SZ /d "127.0.0.1:7890" /f 2>nul
)

echo 请选择要安装的应用类别:
echo.
echo [1] 基础工具 (Chrome, Firefox, 7-Zip, Notepad++)
echo [2] 开发环境 (VS Code, Git, Python, Node.js)
echo [3] 媒体工具 (VLC, IrfanView)
echo [4] 系统工具 (Process Monitor, Wireshark)
echo [5] 全部安装
echo [6] 自定义选择
echo [0] 跳过安装
echo.
set /p choice=请输入选择 (0-6): 

if "%choice%"=="0" goto :end
if "%choice%"=="1" goto :basic
if "%choice%"=="2" goto :dev
if "%choice%"=="3" goto :media
if "%choice%"=="4" goto :system
if "%choice%"=="5" goto :all
if "%choice%"=="6" goto :custom
goto :invalid

:basic
echo 安装基础工具...
call :install_choco
choco install googlechrome firefox 7zip notepadplusplus -y
goto :shortcuts

:dev
echo 安装开发环境...
call :install_choco
choco install vscode git python nodejs -y
goto :shortcuts

:media
echo 安装媒体工具...
call :install_choco
choco install vlc irfanview -y
goto :shortcuts

:system
echo 安装系统工具...
call :install_choco
choco install procmon wireshark -y
goto :shortcuts

:all
echo 安装所有应用...
call :install_choco
choco install googlechrome firefox 7zip notepadplusplus vscode git python nodejs vlc irfanview -y
goto :shortcuts

:custom
echo 自定义安装...
echo 可用应用列表:
echo - googlechrome (Google Chrome)
echo - firefox (Firefox)
echo - 7zip (7-Zip)
echo - notepadplusplus (Notepad++)
echo - vscode (Visual Studio Code)
echo - git (Git)
echo - python (Python)
echo - nodejs (Node.js)
echo - vlc (VLC Media Player)
echo.
set /p apps=请输入要安装的应用名称 (用空格分隔): 
call :install_choco
choco install %apps% -y
goto :shortcuts

:install_choco
echo 安装 Chocolatey...
powershell -Command "Set-ExecutionPolicy Bypass -Scope Process -Force; [System.Net.ServicePointManager]::SecurityProtocol = [System.Net.ServicePointManager]::SecurityProtocol -bor 3072; iex ((New-Object System.Net.WebClient).DownloadString('https://community.chocolatey.org/install.ps1'))"
call refreshenv.cmd 2>nul
set PATH=%PATH%;C:\ProgramData\chocolatey\bin
return

:shortcuts
echo 创建桌面快捷方式...
powershell -Command "
$WshShell = New-Object -comObject WScript.Shell
$apps = @(
    @{Name='Chrome'; Path='C:\Program Files\Google\Chrome\Application\chrome.exe'},
    @{Name='Firefox'; Path='C:\Program Files\Mozilla Firefox\firefox.exe'},
    @{Name='VS Code'; Path='C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\Code.exe'},
    @{Name='Notepad++'; Path='C:\Program Files\Notepad++\notepad++.exe'}
)
foreach ($app in $apps) {
    if (Test-Path $app.Path) {
        $Shortcut = $WshShell.CreateShortcut('C:\Users\<USER>\Desktop\' + $app.Name + '.lnk')
        $Shortcut.TargetPath = $app.Path
        $Shortcut.Save()
    }
}
"
goto :end

:invalid
echo 无效选择，跳过安装
goto :end

:end
echo.
echo 配置完成！沙盒准备就绪。
pause
