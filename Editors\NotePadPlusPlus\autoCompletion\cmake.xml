<?xml version="1.0" encoding="UTF-8" ?>
<NotepadPlus>
	<AutoComplete>
		<Environment ignoreCase="no" startFunc="(" stopFunc=")" paramSeparator="," terminal=";" />
		<KeyWord name="add_custom_command" />
		<KeyWord name="add_compile_definitions" />
		<KeyWord name="add_compile_options" />
		<KeyWord name="add_custom_command" />
		<KeyWord name="add_custom_target" />
		<KeyWord name="add_definitions" />
		<KeyWord name="add_dependencies" />
		<KeyWord name="add_executable" />
		<KeyWord name="add_library" />
		<KeyWord name="add_link_options" />
		<KeyWord name="add_subdirectory" />
		<KeyWord name="add_test" />
		<KeyWord name="aux_source_directory" />
		<KeyWord name="build_command" />
		<KeyWord name="build_name" />
		<KeyWord name="cmake_minimum_required" />
		<KeyWord name="configure_file" />
		<KeyWord name="create_test_sourcelist" />
		<KeyWord name="else" />
		<KeyWord name="elseif" />
		<KeyWord name="enable_language" />
		<KeyWord name="enable_testing" />
		<KeyWord name="endforeach" />
		<KeyWord name="endfunction" />
		<KeyWord name="endif" />
		<KeyWord name="endmacro" />
		<KeyWord name="endwhile" />
		<KeyWord name="exec_program" />
		<KeyWord name="execute_process" />
		<KeyWord name="export" />
		<KeyWord name="export_library_dependencies" />
		<KeyWord name="file" />
		<KeyWord name="find_file" />
		<KeyWord name="find_library" />
		<KeyWord name="find_package" />
		<KeyWord name="find_path" />
		<KeyWord name="find_program" />
		<KeyWord name="fltk_wrap_ui" />
		<KeyWord name="foreach" />
		<KeyWord name="function" />
		<KeyWord name="get_cmake_property" />
		<KeyWord name="get_directory_property" />
		<KeyWord name="get_filename_component" />
		<KeyWord name="get_source_file_property" />
		<KeyWord name="get_target_property" />
		<KeyWord name="get_test_property" />
		<KeyWord name="if" />
		<KeyWord name="include" />
		<KeyWord name="include_directories" />
		<KeyWord name="include_external_msproject" />
		<KeyWord name="include_regular_expression" />
		<KeyWord name="install" />
		<KeyWord name="install_files" />
		<KeyWord name="install_programs" />
		<KeyWord name="install_targets" />
		<KeyWord name="link_directories" />
		<KeyWord name="link_libraries" />
		<KeyWord name="list" />
		<KeyWord name="load_cache" />
		<KeyWord name="load_command" />
		<KeyWord name="macro" />
		<KeyWord name="make_directory" />
		<KeyWord name="mark_as_advanced" />
		<KeyWord name="math" />
		<KeyWord name="message" />
		<KeyWord name="option" />
		<KeyWord name="output_required_files" />
		<KeyWord name="project" />
		<KeyWord name="qt_wrap_cpp" />
		<KeyWord name="qt_wrap_ui" />
		<KeyWord name="remove" />
		<KeyWord name="remove_definitions" />
		<KeyWord name="separate_arguments" />
		<KeyWord name="set" />
		<KeyWord name="set_directory_properties" />
		<KeyWord name="set_source_files_properties" />
		<KeyWord name="set_target_properties" />
		<KeyWord name="set_tests_properties" />
		<KeyWord name="site_name" />
		<KeyWord name="source_group" />
		<KeyWord name="string" />
		<KeyWord name="subdir_depends" />
		<KeyWord name="subdirs" />
		<KeyWord name="target_compile_definitions" />
		<KeyWord name="target_compile_features" />
		<KeyWord name="target_compile_options" />
		<KeyWord name="target_include_directories" />
		<KeyWord name="target_link_directories" />
		<KeyWord name="target_link_libraries" />
		<KeyWord name="target_link_options" />
		<KeyWord name="target_sources" />
		<KeyWord name="try_compile" />
		<KeyWord name="try_run" />
		<KeyWord name="use_mangled_mesa" />
		<KeyWord name="utility_source" />
		<KeyWord name="variable_requires" />
		<KeyWord name="vtk_make_instantiator" />
		<KeyWord name="vtk_wrap_java" />
		<KeyWord name="vtk_wrap_python" />
		<KeyWord name="vtk_wrap_tcl" />
		<KeyWord name="while" />
		<KeyWord name="write_file" />
		<KeyWord name="ABSOLUTE" />
		<KeyWord name="ABSTRACT" />
		<KeyWord name="ADDITIONAL_MAKE_CLEAN_FILES" />
		<KeyWord name="ALL" />
		<KeyWord name="AND" />
		<KeyWord name="APPEND" />
		<KeyWord name="ARGS" />
		<KeyWord name="ASCII" />
		<KeyWord name="BEFORE" />
		<KeyWord name="CACHE" />
		<KeyWord name="CACHE_VARIABLES" />
		<KeyWord name="CLEAR" />
		<KeyWord name="COMMAND" />
		<KeyWord name="COMMANDS" />
		<KeyWord name="COMMAND_NAME" />
		<KeyWord name="COMMENT" />
		<KeyWord name="COMPARE" />
		<KeyWord name="COMPILE_FLAGS" />
		<KeyWord name="COPYONLY" />
		<KeyWord name="DEFINED" />
		<KeyWord name="DEFINE_SYMBOL" />
		<KeyWord name="DEPENDS" />
		<KeyWord name="DOC" />
		<KeyWord name="EQUAL" />
		<KeyWord name="ESCAPE_QUOTES" />
		<KeyWord name="EXCLUDE" />
		<KeyWord name="EXCLUDE_FROM_ALL" />
		<KeyWord name="EXISTS" />
		<KeyWord name="EXPORT_MACRO" />
		<KeyWord name="EXT" />
		<KeyWord name="EXTRA_INCLUDE" />
		<KeyWord name="FATAL_ERROR" />
		<KeyWord name="FILE" />
		<KeyWord name="FILES" />
		<KeyWord name="FORCE" />
		<KeyWord name="FUNCTION" />
		<KeyWord name="GENERATED" />
		<KeyWord name="GLOB" />
		<KeyWord name="GLOB_RECURSE" />
		<KeyWord name="GREATER" />
		<KeyWord name="GROUP_SIZE" />
		<KeyWord name="HEADER_FILE_ONLY" />
		<KeyWord name="HEADER_LOCATION" />
		<KeyWord name="IMMEDIATE" />
		<KeyWord name="INCLUDES" />
		<KeyWord name="INCLUDE_DIRECTORIES" />
		<KeyWord name="INCLUDE_INTERNALS" />
		<KeyWord name="INCLUDE_REGULAR_EXPRESSION" />
		<KeyWord name="LESS" />
		<KeyWord name="LINK_DIRECTORIES" />
		<KeyWord name="LINK_FLAGS" />
		<KeyWord name="LOCATION" />
		<KeyWord name="MACOSX_BUNDLE" />
		<KeyWord name="MACROS" />
		<KeyWord name="MAIN_DEPENDENCY" />
		<KeyWord name="MAKE_DIRECTORY" />
		<KeyWord name="MATCH" />
		<KeyWord name="MATCHALL" />
		<KeyWord name="MATCHES" />
		<KeyWord name="MODULE" />
		<KeyWord name="NAME" />
		<KeyWord name="NAME_WE" />
		<KeyWord name="NOT" />
		<KeyWord name="NOTEQUAL" />
		<KeyWord name="NO_SYSTEM_PATH" />
		<KeyWord name="OBJECT_DEPENDS" />
		<KeyWord name="OPTIONAL" />
		<KeyWord name="OR" />
		<KeyWord name="OUTPUT" />
		<KeyWord name="OUTPUT_VARIABLE" />
		<KeyWord name="PATH" />
		<KeyWord name="PATHS" />
		<KeyWord name="POST_BUILD" />
		<KeyWord name="POST_INSTALL_SCRIPT" />
		<KeyWord name="PREFIX" />
		<KeyWord name="PREORDER" />
		<KeyWord name="PRE_BUILD" />
		<KeyWord name="PRE_INSTALL_SCRIPT" />
		<KeyWord name="PRE_LINK" />
		<KeyWord name="PROGRAM" />
		<KeyWord name="PROGRAM_ARGS" />
		<KeyWord name="PROPERTIES" />
		<KeyWord name="QUIET" />
		<KeyWord name="RANGE" />
		<KeyWord name="READ" />
		<KeyWord name="REGEX" />
		<KeyWord name="REGULAR_EXPRESSION" />
		<KeyWord name="REPLACE" />
		<KeyWord name="REQUIRED" />
		<KeyWord name="RETURN_VALUE" />
		<KeyWord name="RUNTIME_DIRECTORY" />
		<KeyWord name="SEND_ERROR" />
		<KeyWord name="SHARED" />
		<KeyWord name="SOURCES" />
		<KeyWord name="STATIC" />
		<KeyWord name="STATUS" />
		<KeyWord name="STREQUAL" />
		<KeyWord name="STRGREATER" />
		<KeyWord name="STRLESS" />
		<KeyWord name="SUFFIX" />
		<KeyWord name="TARGET" />
		<KeyWord name="TOLOWER" />
		<KeyWord name="TOUPPER" />
		<KeyWord name="VAR" />
		<KeyWord name="VARIABLES" />
		<KeyWord name="VERSION" />
		<KeyWord name="WIN32" />
		<KeyWord name="WRAP_EXCLUDE" />
		<KeyWord name="WRITE" />
		<KeyWord name="APPLE" />
		<KeyWord name="MINGW" />
		<KeyWord name="MSYS" />
		<KeyWord name="CYGWIN" />
		<KeyWord name="BORLAND" />
		<KeyWord name="WATCOM" />
		<KeyWord name="MSVC" />
		<KeyWord name="MSVC_IDE" />
		<KeyWord name="MSVC60" />
		<KeyWord name="MSVC70" />
		<KeyWord name="MSVC71" />
		<KeyWord name="MSVC80" />
		<KeyWord name="CMAKE_COMPILER_2005" />
		<KeyWord name="OFF" />
		<KeyWord name="ON" />
	</AutoComplete>
</NotepadPlus>
