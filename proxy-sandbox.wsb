<?xml version="1.0" encoding="UTF-8"?>
<Configuration>
  <!-- 
    Windows Sandbox 代理配置文件
    
    使用说明:
    1. 将此文件和 start.bat 放在同一目录下
    2. 双击此文件启动配置好的沙盒
    3. 沙盒启动后会自动配置代理
    
    注意事项:
    - 请确保主机代理软件正在运行
    - 请确保防火墙已允许7890端口访问
    - 如果路径不同，请修改下面的 HostFolder 路径
  -->
  
  <!-- 映射主机文件夹到沙盒 -->
  <MappedFolders>
    <MappedFolder>
      <!-- 主机文件夹路径 - 请根据实际情况修改 -->
      <HostFolder>e:\desk\ceshi</HostFolder>
      <!-- 沙盒内的映射路径 -->
      <SandboxFolder>C:\Users\<USER>\Desktop\config</SandboxFolder>
      <!-- 允许读写 -->
      <ReadOnly>false</ReadOnly>
    </MappedFolder>
  </MappedFolders>
  
  <!-- 沙盒启动时自动执行的命令 -->
  <LogonCommand>
    <Command>C:\Users\<USER>\Desktop\config\start.bat</Command>
  </LogonCommand>
  
  <!-- 启用网络连接 -->
  <Networking>Enable</Networking>
  
  <!-- 启用vGPU支持（可选，提升性能） -->
  <VGpu>Enable</VGpu>
  
  <!-- 启用音频支持（可选） -->
  <AudioInput>Enable</AudioInput>
  
  <!-- 启用视频输入支持（可选） -->
  <VideoInput>Enable</VideoInput>
  
  <!-- 启用打印机重定向（可选） -->
  <PrinterRedirection>Enable</PrinterRedirection>
  
  <!-- 启用剪贴板重定向（可选） -->
  <ClipboardRedirection>Enable</ClipboardRedirection>
  
  <!-- 内存分配（可选，单位MB，默认为主机内存的一半） -->
  <MemoryInMB>2048</MemoryInMB>
  
</Configuration>
