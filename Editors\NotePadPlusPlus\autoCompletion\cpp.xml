<?xml version="1.0" encoding="UTF-8" ?>
<NotepadPlus>
	<!-- language doesnt really mean anything, its more of a comment -->
	<AutoComplete language="C++">
		<!--
		Environment specifies how the language should be interpreted. ignoreCase makes autocomplete
		ignore any casing, start and stopFunc specify what chars a function starts and stops with.
		param specifies parameter separator and terminal can be used to specify a character that stops
		any function. Using the same character for different functions results in undefined behaviour.
		
		05/11/2009
		The basic word character are : A-Z a-z 0-9 and '_' 
		If your function name contains other characters,
		add your characters in "additionalWordChar" attribute (without separator)
		in order to make calltip hint work
		-->
		<Environment ignoreCase="no" startFunc="(" stopFunc=")" paramSeparator="," terminal=";" additionalWordChar=""/>
		<!--
		The following items should be alphabetically ordered.
		func="yes" means the keyword should be treated as a fuction, and thus can be used in the parameter
		calltip system. If this is the case, the retVal attribute specifies the return value/type. Any
		following Param tag specifies a parameter, they must be in order. The name attributes specifies
		the parameter name.
		-->
		<KeyWord name="#define" />
		<KeyWord name="#elif" />
		<KeyWord name="#else" />
		<KeyWord name="#endif" />
		<KeyWord name="#error" />
		<KeyWord name="#if" />
		<KeyWord name="#ifdef" />
		<KeyWord name="#ifndef" />
		<KeyWord name="#include" />
		<KeyWord name="#line" />
		<KeyWord name="#pragma" />
		<KeyWord name="#undef" />
		<KeyWord name="NULL" />
		<KeyWord name="abort" func="yes">
			<Overload retVal="void" >
				<Param name="void" />
			</Overload>
		</KeyWord>
		<KeyWord name="abs" func="yes">
			<Overload retVal="int" >
				<Param name="int i" />
			</Overload>
		</KeyWord>
		<KeyWord name="absread" />
		<KeyWord name="abswrite" />
		<KeyWord name="access" func="yes">
			<Overload retVal="int" >
				<Param name="const char *path" />
				<Param name="int amode" />
			</Overload>
		</KeyWord>
		<KeyWord name="accumulate" />
		<KeyWord name="acos" func="yes">
			<Overload retVal="double" >
				<Param name="double x" />
			</Overload>
		</KeyWord>
		<KeyWord name="acosl" />
		<KeyWord name="address" />
		<KeyWord name="adjacent_difference" />
		<KeyWord name="adjacent_find" />
		<KeyWord name="advance" />
		<KeyWord name="alignas" />
		<KeyWord name="alignof" />
		<KeyWord name="allocate" />
		<KeyWord name="allocator" />
		<KeyWord name="allocmem" />
		<KeyWord name="always_noconv" />
		<KeyWord name="and" />
		<KeyWord name="and_eq" />
		<KeyWord name="any" />
		<KeyWord name="append" />
		<KeyWord name="arc" />
		<KeyWord name="arg" />
		<KeyWord name="asctime" func="yes">
			<Overload retVal="char*" >
				<Param name="const struct tm *timeptr" />
			</Overload>
		</KeyWord>
		<KeyWord name="asin" func="yes">
			<Overload retVal="double" >
				<Param name="double x" />
			</Overload>
		</KeyWord>
		<KeyWord name="asinl" />
		<KeyWord name="asm" />
		<KeyWord name="assert" func="yes">
			<Overload retVal="void" >
				<Param name="int expression" />
			</Overload>
		</KeyWord>
		<KeyWord name="assign" />
		<KeyWord name="at" />
		<KeyWord name="atan" func="yes">
			<Overload retVal="double" >
				<Param name="double x" />
			</Overload>
		</KeyWord>
		<KeyWord name="atan2" func="yes">
			<Overload retVal="double" >
				<Param name="double y" />
				<Param name="double x" />
			</Overload>
		</KeyWord>
		<KeyWord name="atan2l" />
		<KeyWord name="atanl" />
		<KeyWord name="atexit" func="yes">
			<Overload retVal="int" >
				<Param name="void (*func)(void)" />
			</Overload>
		</KeyWord>
		<KeyWord name="atof" func="yes">
			<Overload retVal="double" >
				<Param name="const char *nptr" />
			</Overload>
		</KeyWord>
		<KeyWord name="atoi" func="yes">
			<Overload retVal="int" >
				<Param name="const char *nptr" />
			</Overload>
		</KeyWord>
		<KeyWord name="atol" func="yes">
			<Overload retVal="long int" >
				<Param name="const char *nptr" />
			</Overload>
		</KeyWord>
		<KeyWord name="atomic_cancel" />
		<KeyWord name="atomic_commit" />
		<KeyWord name="atomic_noexcept" />
		<KeyWord name="auto" />
		<KeyWord name="auto_ptr" />
		<KeyWord name="back" />
		<KeyWord name="back_inserter" />
		<KeyWord name="back_insert_iterator" />
		<KeyWord name="bad" />
		<KeyWord name="bar" />
		<KeyWord name="bar3d" />
		<KeyWord name="basic_string" />
		<KeyWord name="bcd" />
		<KeyWord name="bdos" />
		<KeyWord name="bdosptr" />
		<KeyWord name="begin" />
		<KeyWord name="bidirectional_iterator" />
		<KeyWord name="binary_function" />
		<KeyWord name="binary_negate" />
		<KeyWord name="binary_search" />
		<KeyWord name="bind1st" />
		<KeyWord name="bind2nd" />
		<KeyWord name="binder1st" />
		<KeyWord name="binder2nd" />
		<KeyWord name="bioscom" />
		<KeyWord name="biosdisk" />
		<KeyWord name="biosequip" />
		<KeyWord name="bioskey" />
		<KeyWord name="biosmemory" />
		<KeyWord name="biosprint" />
		<KeyWord name="biostime" />
		<KeyWord name="bitand" />
		<KeyWord name="bitor" />
		<KeyWord name="bitset" />
		<KeyWord name="bool" />
		<KeyWord name="boolalpha" />
		<KeyWord name="break" />
		<KeyWord name="brk" />
		<KeyWord name="bsearch" func="yes">
			<Overload retVal="void *" >
				<Param name="const void *key" />
				<Param name="const void *base" />
				<Param name="size_t nmemb" />
				<Param name="size_t size" />
				<Param name="int (*compar)(const void *, const void *)" />
			</Overload>
		</KeyWord>
		<KeyWord name="cabs" />
		<KeyWord name="cabsl" />
		<KeyWord name="calloc" func="yes">
			<Overload retVal="void *" >
				<Param name="size_t nmemb" />
				<Param name="size_t size" />
			</Overload>
		</KeyWord>
		<KeyWord name="capacity" />
		<KeyWord name="case" />
		<KeyWord name="catch" />
		<KeyWord name="category" />
		<KeyWord name="ceil" func="yes">
			<Overload retVal="double" >
				<Param name="double x" />
			</Overload>
		</KeyWord>
		<KeyWord name="ceill" />
		<KeyWord name="cerr" />
		<KeyWord name="cgets" />
		<KeyWord name="char" />
		<KeyWord name="char16_t" />
		<KeyWord name="char32_t" />
		<KeyWord name="char8_t" />
		<KeyWord name="char_type" />
		<KeyWord name="chdir" func="yes">
			<Overload retVal="int" >
				<Param name="const char *path" />
			</Overload>
		</KeyWord>
		<KeyWord name="chmod" func="yes">
			<Overload retVal="int" >
				<Param name="const char *path" />
				<Param name="mode_t mode" />
			</Overload>
		</KeyWord>
		<KeyWord name="chsize" />
		<KeyWord name="cin" />
		<KeyWord name="circle" />
		<KeyWord name="class" />
		<KeyWord name="classic_table" />
		<KeyWord name="clear" />
		<KeyWord name="cleardevice" />
		<KeyWord name="clearerr" func="yes">
			<Overload retVal="void" >
				<Param name="FILE *stream" />
			</Overload>
		</KeyWord>
		<KeyWord name="clearviewport" />
		<KeyWord name="clock" func="yes">
			<Overload retVal="clock_t" >
				<Param name="void" />
			</Overload>
		</KeyWord>
		<KeyWord name="clock_t" />
		<KeyWord name="clog" />
		<KeyWord name="close" func="yes">
			<Overload retVal="int" >
				<Param name="int filedes" />
			</Overload>
		</KeyWord>
		<KeyWord name="closedir" func="yes">
			<Overload retVal="int" >
				<Param name="DIR *dirp" />
			</Overload>
		</KeyWord>
		<KeyWord name="closegraph" />
		<KeyWord name="clreol" />
		<KeyWord name="clrscr" />
		<KeyWord name="co_await" />
		<KeyWord name="co_return" />
		<KeyWord name="co_yield" />
		<KeyWord name="compare" />
		<KeyWord name="compl" />
		<KeyWord name="complex" />
		<KeyWord name="concept" />
		<KeyWord name="conj" />
		<KeyWord name="const" />
		<KeyWord name="construct" />
		<KeyWord name="const_cast" />
		<KeyWord name="const_pointer" />
		<KeyWord name="const_reference" />
		<KeyWord name="consteval" />
		<KeyWord name="constexpr" />
		<KeyWord name="constinit" />
		<KeyWord name="container" />
		<KeyWord name="continue" />
		<KeyWord name="copy" />
		<KeyWord name="copyfmt" />
		<KeyWord name="copy_backward" />
		<KeyWord name="coreleft" />
		<KeyWord name="cos" func="yes">
			<Overload retVal="double" >
				<Param name="double x" />
			</Overload>
		</KeyWord>
		<KeyWord name="cosh" func="yes">
			<Overload retVal="double" >
				<Param name="double x" />
			</Overload>
		</KeyWord>
		<KeyWord name="coshl" />
		<KeyWord name="cosl" />
		<KeyWord name="count" />
		<KeyWord name="country" />
		<KeyWord name="count_if" />
		<KeyWord name="cout" />
		<KeyWord name="cprintf" />
		<KeyWord name="cputs" />
		<KeyWord name="creat" func="yes">
			<Overload retVal="int" >
				<Param name="const char *path" />
				<Param name="mode_t mode" />
			</Overload>
		</KeyWord>
		<KeyWord name="creatnew" />
		<KeyWord name="creattemp" />
		<KeyWord name="cscanf" />
		<KeyWord name="ctime" func="yes">
			<Overload retVal="char *" >
				<Param name="const time_t *timer" />
			</Overload>
		</KeyWord>
		<KeyWord name="ctrlbrk" />
		<KeyWord name="curr_symbol" />
		<KeyWord name="c_str" />
		<KeyWord name="data" />
		<KeyWord name="date_order" />
		<KeyWord name="deallocate" />
		<KeyWord name="dec" />
		<KeyWord name="decimal_point" />
		<KeyWord name="decltype" />
		<KeyWord name="default" />
		<KeyWord name="delay" />
		<KeyWord name="delete" />
		<KeyWord name="delline" />
		<KeyWord name="denorm_min" />
		<KeyWord name="deque" />
		<KeyWord name="destroy" />
		<KeyWord name="detectgraph" />
		<KeyWord name="difference_type" />
		<KeyWord name="difftime" func="yes">
			<Overload retVal="double" >
				<Param name="time_t time1" />
				<Param name="time_t time0" />
			</Overload>
		</KeyWord>
		<KeyWord name="digits" />
		<KeyWord name="digits10" />
		<KeyWord name="disable" />
		<KeyWord name="distance" />
		<KeyWord name="div" func="yes">
			<Overload retVal="div_t" >
				<Param name="int numer" />
				<Param name="int denom" />
			</Overload>
		</KeyWord>
		<KeyWord name="divides" />
		<KeyWord name="dllexport" />
		<KeyWord name="dllexport2" />
		<KeyWord name="dllimport" />
		<KeyWord name="dllimport2" />
		<KeyWord name="do" />
		<KeyWord name="dosexterr" />
		<KeyWord name="dostounix" />
		<KeyWord name="double" />
		<KeyWord name="do_always_noconv" />
		<KeyWord name="do_close" />
		<KeyWord name="do_compare" />
		<KeyWord name="do_curr_symbol" />
		<KeyWord name="do_decimal_point" />
		<KeyWord name="do_encoding" />
		<KeyWord name="do_get" />
		<KeyWord name="do_grouping" />
		<KeyWord name="do_hash" />
		<KeyWord name="do_in" />
		<KeyWord name="do_is" />
		<KeyWord name="do_length" />
		<KeyWord name="do_max_length" />
		<KeyWord name="do_narrow" />
		<KeyWord name="do_neg_format" />
		<KeyWord name="do_open" />
		<KeyWord name="do_out" />
		<KeyWord name="do_pos_format" />
		<KeyWord name="do_scan_is" />
		<KeyWord name="do_scan_not" />
		<KeyWord name="do_thousands_sep" />
		<KeyWord name="do_tolower" />
		<KeyWord name="do_toupper" />
		<KeyWord name="do_transform" />
		<KeyWord name="do_widen" />
		<KeyWord name="drawpoly" />
		<KeyWord name="dup" func="yes">
			<Overload retVal="int" >
				<Param name="int filedes" />
			</Overload>
		</KeyWord>
		<KeyWord name="dup2" func="yes">
			<Overload retVal="int" >
				<Param name="int filedes" />
				<Param name="int filedes2" />
			</Overload>
		</KeyWord>
		<KeyWord name="dynamic_cast" />
		<KeyWord name="eback" />
		<KeyWord name="ecvt" />
		<KeyWord name="egptr" />
		<KeyWord name="ellipse" />
		<KeyWord name="else" />
		<KeyWord name="empty" />
		<KeyWord name="enable" />
		<KeyWord name="encoding" />
		<KeyWord name="end" />
		<KeyWord name="endl" />
		<KeyWord name="ends" />
		<KeyWord name="enum" />
		<KeyWord name="eof" />
		<KeyWord name="epptr" />
		<KeyWord name="epsilon" />
		<KeyWord name="eq" />
		<KeyWord name="equal" />
		<KeyWord name="equal_range" />
		<KeyWord name="equal_to" />
		<KeyWord name="eq_int_type" />
		<KeyWord name="erase" />
		<KeyWord name="event_callback" />
		<KeyWord name="exceptions" />
		<KeyWord name="execl" func="yes">
			<Overload retVal="int" >
				<Param name="const char *path" />
				<Param name="const char *args" />
				<Param name="..." />
			</Overload>
		</KeyWord>
		<KeyWord name="execle" func="yes">
			<Overload retVal="int" >
				<Param name="const char *path" />
				<Param name="const char *args" />
				<Param name="..." />
			</Overload>
		</KeyWord>
		<KeyWord name="execlp" func="yes">
			<Overload retVal="int" >
				<Param name="const char *file" />
				<Param name="const char *args" />
				<Param name="..." />
			</Overload>
		</KeyWord>
		<KeyWord name="execlpe" />
		<KeyWord name="execv" func="yes">
			<Overload retVal="int" >
				<Param name="const char *path" />
				<Param name="char *const argv[]" />
			</Overload>
		</KeyWord>
		<KeyWord name="execve" func="yes">
			<Overload retVal="int" >
				<Param name="const char *path" />
				<Param name="char *const argv[]" />
				<Param name="char *const *envp" />
			</Overload>
		</KeyWord>
		<KeyWord name="execvp" func="yes">
			<Overload retVal="int" >
				<Param name="const char *file" />
				<Param name="char *const argv[]" />
			</Overload>
		</KeyWord>
		<KeyWord name="execvpe" />
		<KeyWord name="exit" func="yes">
			<Overload retVal="void" >
				<Param name="int status" />
			</Overload>
		</KeyWord>
		<KeyWord name="exp" func="yes">
			<Overload retVal="double" >
				<Param name="double x" />
			</Overload>
		</KeyWord>
		<KeyWord name="expl" />
		<KeyWord name="explicit" />
		<KeyWord name="export" />
		<KeyWord name="extern" />
		<KeyWord name="extern_type" />
		<KeyWord name="fabs" func="yes">
			<Overload retVal="double" >
				<Param name="double x" />
			</Overload>
		</KeyWord>
		<KeyWord name="fabsl" />
		<KeyWord name="facet" />
		<KeyWord name="fail" />
		<KeyWord name="failed" />
		<KeyWord name="failure" />
		<KeyWord name="false" />
		<KeyWord name="falsename" />
		<KeyWord name="farcalloc" />
		<KeyWord name="farcoreleft" />
		<KeyWord name="farfree" />
		<KeyWord name="farheapcheck" />
		<KeyWord name="farheapcheckfree" />
		<KeyWord name="farheapchecknode" />
		<KeyWord name="farheapfillfree" />
		<KeyWord name="farheapwalk" />
		<KeyWord name="farmalloc" />
		<KeyWord name="farrealloc" />
		<KeyWord name="fclose" func="yes">
			<Overload retVal="int" >
				<Param name="File *stream" />
			</Overload>
		</KeyWord>
		<KeyWord name="fcloseall" />
		<KeyWord name="fcvt" />
		<KeyWord name="fdopen" func="yes">
			<Overload retVal="File *" >
				<Param name="int filedes" />
				<Param name="const char *type" />
			</Overload>
		</KeyWord>
		<KeyWord name="feof" func="yes">
			<Overload retVal="int" >
				<Param name="FILE *stream" />
			</Overload>
		</KeyWord>
		<KeyWord name="ferror" func="yes">
			<Overload retVal="int" >
				<Param name="FILE *stream" />
			</Overload>
		</KeyWord>
		<KeyWord name="fflush" func="yes">
			<Overload retVal="int" >
				<Param name="FILE *stream" />
			</Overload>
		</KeyWord>
		<KeyWord name="fgetc" func="yes">
			<Overload retVal="int" >
				<Param name="FILE *stream" />
			</Overload>
		</KeyWord>
		<KeyWord name="fgetchar" />
		<KeyWord name="fgetpos" func="yes">
			<Overload retVal="int" >
				<Param name="FILE *stream" />
				<Param name="fpos_t *pos" />
			</Overload>
		</KeyWord>
		<KeyWord name="fgets" func="yes">
			<Overload retVal="char *" >
				<Param name="char *s" />
				<Param name="int n" />
				<Param name="FILE *stream" />
			</Overload>
		</KeyWord>
		<KeyWord name="filebuf" />
		<KeyWord name="filelength" />
		<KeyWord name="fileno" func="yes">
			<Overload retVal="int" >
				<Param name="FILE *stream" />
			</Overload>
		</KeyWord>
		<KeyWord name="fill" />
		<KeyWord name="fillellipse" />
		<KeyWord name="fillpoly" />
		<KeyWord name="fill_n" />
		<KeyWord name="final" />
		<KeyWord name="find" />
		<KeyWord name="findfirst" />
		<KeyWord name="findnext" />
		<KeyWord name="find_end" />
		<KeyWord name="find_first_not_of" />
		<KeyWord name="find_first_of" />
		<KeyWord name="find_if" />
		<KeyWord name="find_last_not_of" />
		<KeyWord name="find_last_of" />
		<KeyWord name="fixed" />
		<KeyWord name="flags" />
		<KeyWord name="flip" />
		<KeyWord name="float" />
		<KeyWord name="floodfill" />
		<KeyWord name="floor" func="yes">
			<Overload retVal="double" >
				<Param name="double x" />
			</Overload>
		</KeyWord>
		<KeyWord name="floorl" />
		<KeyWord name="flush" />
		<KeyWord name="flushall" />
		<KeyWord name="fmod" func="yes">
			<Overload retVal="double" >
				<Param name="double x" />
				<Param name="double y" />
			</Overload>
		</KeyWord>
		<KeyWord name="fmodl" />
		<KeyWord name="fmtflags" />
		<KeyWord name="fnmerge" />
		<KeyWord name="fnsplit" />
		<KeyWord name="fopen" func="yes">
			<Overload retVal="FILE *" >
				<Param name="const char * file" />
				<Param name="const char * mode" />
			</Overload>
		</KeyWord>
		<KeyWord name="for" />
		<KeyWord name="for_each" />
		<KeyWord name="forward_iterator" />
		<KeyWord name="fprintf" func="yes">
			<Overload retVal="int" >
				<Param name="FILE *stream" />
				<Param name="const char *format" />
				<Param name="..." />
			</Overload>
		</KeyWord>
		<KeyWord name="fputc" func="yes">
			<Overload retVal="int" >
				<Param name="int c" />
				<Param name="FILE *stream" />
			</Overload>
		</KeyWord>
		<KeyWord name="fputchar" />
		<KeyWord name="fputs" func="yes">
			<Overload retVal="int" >
				<Param name="const char *s" />
				<Param name="FILE *stream" />
			</Overload>
		</KeyWord>
		<KeyWord name="FP_OFF" />
		<KeyWord name="FP_SEG" />
		<KeyWord name="frac_digits" />
		<KeyWord name="fread" func="yes">
			<Overload retVal="size_t" >
				<Param name="void *ptr" />
				<Param name="size_t size" />
				<Param name="size_t nmemb" />
				<Param name="FILE *stream" />
			</Overload>
		</KeyWord>
		<KeyWord name="free" func="yes">
			<Overload retVal="void" >
				<Param name="void *ptr" />
			</Overload>
		</KeyWord>
		<KeyWord name="freemem" />
		<KeyWord name="freeze" />
		<KeyWord name="freopen" func="yes">
			<Overload retVal="FILE *" >
				<Param name="const char *filename" />
				<Param name="const char *mode" />
				<Param name="FILE *stream" />
			</Overload>
		</KeyWord>
		<KeyWord name="frexp" func="yes">
			<Overload retVal="double" >
				<Param name="double value" />
				<Param name="int *exp" />
			</Overload>
		</KeyWord>
		<KeyWord name="frexpl" />
		<KeyWord name="friend" />
		<KeyWord name="front" />
		<KeyWord name="front_inserter" />
		<KeyWord name="front_insert_iterator" />
		<KeyWord name="fscanf" func="yes">
			<Overload retVal="int" >
				<Param name="FILE *stream" />
				<Param name="const char *format" />
				<Param name="..." />
			</Overload>
		</KeyWord>
		<KeyWord name="fseek" func="yes">
			<Overload retVal="int" >
				<Param name="FILE *stream" />
				<Param name="long int offset" />
				<Param name="int whence" />
			</Overload>
		</KeyWord>
		<KeyWord name="fsetpos" func="yes">
			<Overload retVal="int" >
				<Param name="FILE *stream" />
				<Param name="const fpos_t * pos" />
			</Overload>
		</KeyWord>
		<KeyWord name="fstat" func="yes">
			<Overload retVal="int" >
				<Param name="int filedes" />
				<Param name="struct stat *buf" />
			</Overload>
		</KeyWord>
		<KeyWord name="fstream" />
		<KeyWord name="ftell" func="yes">
			<Overload retVal="long int" >
				<Param name="FILE *stream" />
			</Overload>
		</KeyWord>
		<KeyWord name="ftime" />
		<KeyWord name="fwrite" func="yes">
			<Overload retVal="size_t" >
				<Param name="const void *ptr" />
				<Param name="size_t size" />
				<Param name="size_t nmemb" />
				<Param name="FILE *stream" />
			</Overload>
		</KeyWord>
		<KeyWord name="gbump" />
		<KeyWord name="gcount" />
		<KeyWord name="gcvt" />
		<KeyWord name="generate" />
		<KeyWord name="generate_n" />
		<KeyWord name="geninterrupt" />
		<KeyWord name="get" />
		<KeyWord name="getarccoords" />
		<KeyWord name="getaspectratio" />
		<KeyWord name="getbkcolor" />
		<KeyWord name="getc" func="yes">
			<Overload retVal="int" >
				<Param name="FILE *stream" />
			</Overload>
		</KeyWord>
		<KeyWord name="getcbrk" />
		<KeyWord name="getch" />
		<KeyWord name="getchar" func="yes">
			<Overload retVal="int" >
				<Param name="void" />
			</Overload>
		</KeyWord>
		<KeyWord name="getche" />
		<KeyWord name="getcolor" />
		<KeyWord name="getcurdir" />
		<KeyWord name="getcwd" func="yes">
			<Overload retVal="char *" >
				<Param name="char *buf" />
				<Param name="size_t size" />
			</Overload>
		</KeyWord>
		<KeyWord name="getdate" />
		<KeyWord name="getdefaultpalette" />
		<KeyWord name="getdfree" />
		<KeyWord name="getdisk" />
		<KeyWord name="getdrivername" />
		<KeyWord name="getdta" />
		<KeyWord name="getenv" func="yes">
			<Overload retVal="char *" >
				<Param name="const char *name" />
			</Overload>
		</KeyWord>
		<KeyWord name="getfat" />
		<KeyWord name="getfatd" />
		<KeyWord name="getfillpattern" />
		<KeyWord name="getfillsettings" />
		<KeyWord name="getftime" />
		<KeyWord name="getgraphmode" />
		<KeyWord name="getimage" />
		<KeyWord name="getline" />
		<KeyWord name="getlinesettings" />
		<KeyWord name="getloc" />
		<KeyWord name="getmaxcolor" />
		<KeyWord name="getmaxmode" />
		<KeyWord name="getmaxx" />
		<KeyWord name="getmaxy" />
		<KeyWord name="getmodename" />
		<KeyWord name="getmoderange" />
		<KeyWord name="getpalette" />
		<KeyWord name="getpalettesize" />
		<KeyWord name="getpass" />
		<KeyWord name="getpid" func="yes">
			<Overload retVal="pid_t" >
				<Param name="void" />
			</Overload>
		</KeyWord>
		<KeyWord name="getpixel" />
		<KeyWord name="getpsp" />
		<KeyWord name="gets" func="yes">
			<Overload retVal="char *" >
				<Param name="char *s" />
			</Overload>
		</KeyWord>
		<KeyWord name="gettext" />
		<KeyWord name="gettextinfo" />
		<KeyWord name="gettextsettings" />
		<KeyWord name="gettime" />
		<KeyWord name="getvect" />
		<KeyWord name="getverify" />
		<KeyWord name="getviewsettings" />
		<KeyWord name="getw" />
		<KeyWord name="getx" />
		<KeyWord name="gety" />
		<KeyWord name="get_allocator" />
		<KeyWord name="get_date" />
		<KeyWord name="get_monthname" />
		<KeyWord name="get_temporary_buffer" />
		<KeyWord name="get_time" />
		<KeyWord name="get_weekday" />
		<KeyWord name="get_year" />
		<KeyWord name="gmtime" func="yes">
			<Overload retVal="struct tm *" >
				<Param name="const time_t *timer" />
			</Overload>
		</KeyWord>
		<KeyWord name="good" />
		<KeyWord name="goto" />
		<KeyWord name="gotoxy" />
		<KeyWord name="gptr" />
		<KeyWord name="graphdefaults" />
		<KeyWord name="grapherrormsg" />
		<KeyWord name="graphresult" />
		<KeyWord name="greater" />
		<KeyWord name="greater_equal" />
		<KeyWord name="grouping" />
		<KeyWord name="harderr" />
		<KeyWord name="hardresume" />
		<KeyWord name="hardretn" />
		<KeyWord name="hash" />
		<KeyWord name="has_denorm" />
		<KeyWord name="has_infinity" />
		<KeyWord name="has_quiet_NaN" />
		<KeyWord name="has_signaling_NaN" />
		<KeyWord name="heapcheck" />
		<KeyWord name="heapcheckfree" />
		<KeyWord name="heapchecknode" />
		<KeyWord name="heapfillfree" />
		<KeyWord name="heapwalk" />
		<KeyWord name="hex" />
		<KeyWord name="highvideo" />
		<KeyWord name="hypot" />
		<KeyWord name="hypotl" />
		<KeyWord name="id" />
		<KeyWord name="if" />
		<KeyWord name="ifstream" />
		<KeyWord name="ignore" />
		<KeyWord name="imag" />
		<KeyWord name="imagesize" />
		<KeyWord name="imbue" />
		<KeyWord name="in" />
		<KeyWord name="includes" />
		<KeyWord name="infinity" />
		<KeyWord name="init" />
		<KeyWord name="initgraph" />
		<KeyWord name="inline" />
		<KeyWord name="inner_product" />
		<KeyWord name="inp" />
		<KeyWord name="inplace_merge" />
		<KeyWord name="inport" />
		<KeyWord name="inportb" />
		<KeyWord name="input_iterator" />
		<KeyWord name="inpw" />
		<KeyWord name="insert" />
		<KeyWord name="inserter" />
		<KeyWord name="insert_iterator" />
		<KeyWord name="insline" />
		<KeyWord name="installuserdriver" />
		<KeyWord name="installuserfont" />
		<KeyWord name="int" />
		<KeyWord name="int16_t" />
		<KeyWord name="int32_t" />
		<KeyWord name="int64_t" />
		<KeyWord name="int86" />
		<KeyWord name="int86x" />
		<KeyWord name="int8_t" />
		<KeyWord name="int_fast16_t" />
		<KeyWord name="int_fast32_t" />
		<KeyWord name="int_fast64_t" />
		<KeyWord name="int_fast8_t" />
		<KeyWord name="int_least16_t" />
		<KeyWord name="int_least32_t" />
		<KeyWord name="int_least64_t" />
		<KeyWord name="int_least8_t" />
		<KeyWord name="intdos" />
		<KeyWord name="intdosx" />
		<KeyWord name="internal" />
		<KeyWord name="intern_type" />
		<KeyWord name="intmax_t" />
		<KeyWord name="intptr_t" />
		<KeyWord name="Intl" />
		<KeyWord name="intr" />
		<KeyWord name="int_type" />
		<KeyWord name="ioctl" />
		<KeyWord name="ios" />
		<KeyWord name="iostate" />
		<KeyWord name="ios_type" />
		<KeyWord name="is" />
		<KeyWord name="isalnum" func="yes">
			<Overload retVal="int" >
				<Param name="int c" />
			</Overload>
		</KeyWord>
		<KeyWord name="isalpha" func="yes">
			<Overload retVal="int" >
				<Param name="int c" />
			</Overload>
		</KeyWord>
		<KeyWord name="isascii" />
		<KeyWord name="isatty" func="yes">
			<Overload retVal="int" >
				<Param name="int filedes" />
			</Overload>
		</KeyWord>
		<KeyWord name="iscntrl" func="yes">
			<Overload retVal="int" >
				<Param name="int c" />
			</Overload>
		</KeyWord>
		<KeyWord name="isdigit" func="yes">
			<Overload retVal="int" >
				<Param name="int c" />
			</Overload>
		</KeyWord>
		<KeyWord name="isgraph" func="yes">
			<Overload retVal="int" >
				<Param name="int c" />
			</Overload>
		</KeyWord>
		<KeyWord name="islower" func="yes">
			<Overload retVal="int" >
				<Param name="int c" />
			</Overload>
		</KeyWord>
		<KeyWord name="isprint" func="yes">
			<Overload retVal="int" >
				<Param name="int c" />
			</Overload>
		</KeyWord>
		<KeyWord name="ispunct" func="yes">
			<Overload retVal="int" >
				<Param name="int c" />
			</Overload>
		</KeyWord>
		<KeyWord name="isspace" func="yes">
			<Overload retVal="int" >
				<Param name="int c" />
			</Overload>
		</KeyWord>
		<KeyWord name="istream" />
		<KeyWord name="istream_type" />
		<KeyWord name="istringstream" />
		<KeyWord name="isupper" func="yes">
			<Overload retVal="int" >
				<Param name="int c" />
			</Overload>
		</KeyWord>
		<KeyWord name="isxdigit" func="yes">
			<Overload retVal="int" >
				<Param name="int c" />
			</Overload>
		</KeyWord>
		<KeyWord name="is_bounded" />
		<KeyWord name="is_exact" />
		<KeyWord name="is_iec559" />
		<KeyWord name="is_integer" />
		<KeyWord name="is_modulo" />
		<KeyWord name="is_open" />
		<KeyWord name="is_signed" />
		<KeyWord name="is_specialized" />
		<KeyWord name="is_sync" />
		<KeyWord name="iter_swap" />
		<KeyWord name="iter_type" />
		<KeyWord name="itoa" />
		<KeyWord name="iword" />
		<KeyWord name="kbhit" />
		<KeyWord name="keep" />
		<KeyWord name="key_comp" />
		<KeyWord name="labs" func="yes">
			<Overload retVal="long int" >
				<Param name="long int i" />
			</Overload>
		</KeyWord>
		<KeyWord name="ldexp" func="yes">
			<Overload retVal="double" >
				<Param name="double x" />
				<Param name="int exp" />
			</Overload>
		</KeyWord>
		<KeyWord name="ldexpl" />
		<KeyWord name="ldiv" func="yes">
			<Overload retVal="ldiv_t" >
				<Param name="long int numer" />
				<Param name="long int denom" />
			</Overload>
		</KeyWord>
		<KeyWord name="left" />
		<KeyWord name="length" />
		<KeyWord name="less" />
		<KeyWord name="less_equal" />
		<KeyWord name="lexicographical_compare" />
		<KeyWord name="lfind" />
		<KeyWord name="line" />
		<KeyWord name="linerel" />
		<KeyWord name="lineto" />
		<KeyWord name="list" />
		<KeyWord name="localeconv" func="yes">
			<Overload retVal="struct lconv *" >
				<Param name="void" />
			</Overload>
		</KeyWord>
		<KeyWord name="localtime" func="yes">
			<Overload retVal="struct tm *" >
				<Param name="const time_t *timer" />
			</Overload>
		</KeyWord>
		<KeyWord name="lock" />
		<KeyWord name="locking" />
		<KeyWord name="log" func="yes">
			<Overload retVal="double" >
				<Param name="double x" />
			</Overload>
		</KeyWord>
		<KeyWord name="log10" func="yes">
			<Overload retVal="double" >
				<Param name="double x" />
			</Overload>
		</KeyWord>
		<KeyWord name="log10l" />
		<KeyWord name="logical_and" />
		<KeyWord name="logical_not" />
		<KeyWord name="logical_or" />
		<KeyWord name="logl" />
		<KeyWord name="long" />
		<KeyWord name="longjmp" func="yes">
			<Overload retVal="void" >
				<Param name="jmp_buf env" />
				<Param name="int val" />
			</Overload>
		</KeyWord>
		<KeyWord name="lower_bound" />
		<KeyWord name="lowvideo" />
		<KeyWord name="lsearch" />
		<KeyWord name="lseek" func="yes">
			<Overload retVal="off_t" >
				<Param name="int filedes" />
				<Param name="off_t offset" />
				<Param name="int whence" />
			</Overload>
		</KeyWord>
		<KeyWord name="lt" />
		<KeyWord name="ltoa" />
		<KeyWord name="make_heap" />
		<KeyWord name="make_pair" />
		<KeyWord name="malloc" func="yes">
			<Overload retVal="void" >
				<Param name="size_t size" />
			</Overload>
		</KeyWord>
		<KeyWord name="map" />
		<KeyWord name="matherr" />
		<KeyWord name="max" />
		<KeyWord name="max_element" />
		<KeyWord name="max_exponent" />
		<KeyWord name="max_exponent10" />
		<KeyWord name="max_length" />
		<KeyWord name="max_size" />
		<KeyWord name="mblen" func="yes">
			<Overload retVal="int" >
				<Param name="const char *s" />
				<Param name="size_t n" />
			</Overload>
		</KeyWord>
		<KeyWord name="mbstowcs" func="yes">
			<Overload retVal="size_t" >
				<Param name="wchar_t *pwcs" />
				<Param name="const char *s" />
				<Param name="size_t n" />
			</Overload>
		</KeyWord>
		<KeyWord name="mbtowc" func="yes">
			<Overload retVal="int" >
				<Param name="wchar_t *pwc" />
				<Param name="const char *s" />
				<Param name="size_t n" />
			</Overload>
		</KeyWord>
		<KeyWord name="memccpy" />
		<KeyWord name="memchr" func="yes">
			<Overload retVal="void *" >
				<Param name="const void *s" />
				<Param name="int c" />
				<Param name="size_t n" />
			</Overload>
		</KeyWord>
		<KeyWord name="memcmp" func="yes">
			<Overload retVal="int" >
				<Param name="const void *s1" />
				<Param name="const void *s2" />
				<Param name="size_t n" />
			</Overload>
		</KeyWord>
		<KeyWord name="memcpy" func="yes">
			<Overload retVal="void *" >
				<Param name="void *s1" />
				<Param name="const void *s2" />
				<Param name="size_t n" />
			</Overload>
		</KeyWord>
		<KeyWord name="memicmp" />
		<KeyWord name="memmove" func="yes">
			<Overload retVal="void *" >
				<Param name="void * s1" />
				<Param name="const void *s2" />
				<Param name="size_t n" />
			</Overload>
		</KeyWord>
		<KeyWord name="memset" func="yes">
			<Overload retVal="void *" >
				<Param name="void *s" />
				<Param name="int c" />
				<Param name="size_t n" />
			</Overload>
		</KeyWord>
		<KeyWord name="merge" />
		<KeyWord name="min" />
		<KeyWord name="minus" />
		<KeyWord name="min_element" />
		<KeyWord name="min_exponent" />
		<KeyWord name="min_exponent10" />
		<KeyWord name="mismatch" />
		<KeyWord name="mkdir" func="yes">
			<Overload retVal="int" >
				<Param name="const char *path" />
				<Param name="mode_t mode" />
			</Overload>
		</KeyWord>
		<KeyWord name="mktemp" />
		<KeyWord name="mktime" func="yes">
			<Overload retVal="time_t" >
				<Param name="struct tm *timer" />
			</Overload>
		</KeyWord>
		<KeyWord name="MK_FP" />
		<KeyWord name="modf" func="yes">
			<Overload retVal="double" >
				<Param name="double value" />
				<Param name="double *iptr" />
			</Overload>
		</KeyWord>
		<KeyWord name="modfl" />
		<KeyWord name="modulus" />
		<KeyWord name="move" />
		<KeyWord name="movedata" />
		<KeyWord name="moverel" />
		<KeyWord name="movetext" />
		<KeyWord name="moveto" />
		<KeyWord name="movmem" />
		<KeyWord name="multimap" />
		<KeyWord name="multiset" />
		<KeyWord name="mutable" />
		<KeyWord name="name" />
		<KeyWord name="namespace" />
		<KeyWord name="narrow" />
		<KeyWord name="negate" />
		<KeyWord name="negative_sign" />
		<KeyWord name="neg_format" />
		<KeyWord name="new" />
		<KeyWord name="next_permutation" />
		<KeyWord name="noboolalpha" />
		<KeyWord name="noexcept" />
		<KeyWord name="none" />
		<KeyWord name="norm" />
		<KeyWord name="normvideo" />
		<KeyWord name="noshowbase" />
		<KeyWord name="noshowpoint" />
		<KeyWord name="noshowpos" />
		<KeyWord name="noskipws" />
		<KeyWord name="nosound" />
		<KeyWord name="not" />
		<KeyWord name="not1" />
		<KeyWord name="not2" />
		<KeyWord name="not_eof" />
		<KeyWord name="not_eq" />
		<KeyWord name="not_equal_to" />
		<KeyWord name="nounitbuf" />
		<KeyWord name="nouppercase" />
		<KeyWord name="nth_element" />
		<KeyWord name="nullptr" />
		<KeyWord name="numeric_limits" />
		<KeyWord name="oct" />
		<KeyWord name="off_type" />
		<KeyWord name="ofstream" />
		<KeyWord name="open" func="yes">
			<Overload retVal="int" >
				<Param name="const char *path" />
				<Param name="int oflag" />
				<Param name="..." />
			</Overload>
		</KeyWord>
		<KeyWord name="opendir" func="yes">
			<Overload retVal="DIR *" >
				<Param name="const char *dirname" />
			</Overload>
		</KeyWord>
		<KeyWord name="openmode" />
		<KeyWord name="operator!" />
		<KeyWord name="operator!=" />
		<KeyWord name="operator" />
		<KeyWord name="operator&amp;" />
		<KeyWord name="operator&amp;=" />
		<KeyWord name="operator()" />
		<KeyWord name="operator*" />
		<KeyWord name="operator*=" />
		<KeyWord name="operator+" />
		<KeyWord name="operator++" />
		<KeyWord name="operator+=" />
		<KeyWord name="operator-" />
		<KeyWord name="operator-=" />
		<KeyWord name="operator-&gt;" />
		<KeyWord name="operator/" />
		<KeyWord name="operator/=" />
		<KeyWord name="operator&lt;" />
		<KeyWord name="operator&lt;&lt;" />
		<KeyWord name="operator&lt;&lt;=" />
		<KeyWord name="operator&lt;=" />
		<KeyWord name="operator=" />
		<KeyWord name="operator==" />
		<KeyWord name="operator&gt;" />
		<KeyWord name="operator&gt;=" />
		<KeyWord name="operator&gt;&gt;" />
		<KeyWord name="operator&gt;&gt;=" />
		<KeyWord name="operator[]" />
		<KeyWord name="operator^" />
		<KeyWord name="operator^=" />
		<KeyWord name="operator~" />
		<KeyWord name="or" />
		<KeyWord name="or_eq" />
		<KeyWord name="ostream" />
		<KeyWord name="ostream_type" />
		<KeyWord name="ostringstream" />
		<KeyWord name="out" />
		<KeyWord name="outp" />
		<KeyWord name="outport" />
		<KeyWord name="outportb" />
		<KeyWord name="output_iterator" />
		<KeyWord name="outpw" />
		<KeyWord name="outtext" />
		<KeyWord name="outtextxy" />
		<KeyWord name="overflow" />
		<KeyWord name="override" />
		<KeyWord name="pair" />
		<KeyWord name="parsfnm" />
		<KeyWord name="partial_sort" />
		<KeyWord name="partial_sort_copy" />
		<KeyWord name="partial_sum" />
		<KeyWord name="partition" />
		<KeyWord name="pbackfail" />
		<KeyWord name="pbase" />
		<KeyWord name="pbump" />
		<KeyWord name="pcount" />
		<KeyWord name="peek" />
		<KeyWord name="peekb" />
		<KeyWord name="perror" func="yes">
			<Overload retVal="void" >
				<Param name="const char *s" />
			</Overload>
		</KeyWord>
		<KeyWord name="pieslice" />
		<KeyWord name="plus" />
		<KeyWord name="pointer" />
		<KeyWord name="pointer_to_binary_function" />
		<KeyWord name="pointer_to_unary_function" />
		<KeyWord name="poke" />
		<KeyWord name="pokeb" />
		<KeyWord name="polar" />
		<KeyWord name="poly" />
		<KeyWord name="polyl" />
		<KeyWord name="pop" />
		<KeyWord name="pop_back" />
		<KeyWord name="pop_front" />
		<KeyWord name="pop_heap" />
		<KeyWord name="positive_sign" />
		<KeyWord name="pos_format" />
		<KeyWord name="pos_type" />
		<KeyWord name="pow" func="yes">
			<Overload retVal="double" >
				<Param name="double x" />
				<Param name="double y" />
			</Overload>
		</KeyWord>
		<KeyWord name="pow10" />
		<KeyWord name="pow10l" />
		<KeyWord name="powl" />
		<KeyWord name="pptr" />
		<KeyWord name="precision" />
		<KeyWord name="prev_permutation" />
		<KeyWord name="printf" func="yes">
			<Overload retVal="int" >
				<Param name="const char *format" />
				<Param name="..." />
			</Overload>
		</KeyWord>
		<KeyWord name="priority_queue" />
		<KeyWord name="private" />
		<KeyWord name="protected" />
		<KeyWord name="ptr_fun" />
		<KeyWord name="ptrdiff_t" />
		<KeyWord name="pubimbue" />
		<KeyWord name="public" />
		<KeyWord name="pubseekoff" />
		<KeyWord name="pubseekpos" />
		<KeyWord name="pubsetbuf" />
		<KeyWord name="pubsync" />
		<KeyWord name="push" />
		<KeyWord name="push_back" />
		<KeyWord name="push_front" />
		<KeyWord name="push_heap" />
		<KeyWord name="put" />
		<KeyWord name="putback" />
		<KeyWord name="putc" func="yes">
			<Overload retVal="int" >
				<Param name="int c" />
				<Param name="FILE *stream" />
			</Overload>
		</KeyWord>
		<KeyWord name="putch" />
		<KeyWord name="putchar" func="yes">
			<Overload retVal="int" >
				<Param name="int c" />
			</Overload>
		</KeyWord>
		<KeyWord name="putenv" />
		<KeyWord name="putimage" />
		<KeyWord name="putpixel" />
		<KeyWord name="puts" func="yes">
			<Overload retVal="int" >
				<Param name="const char *s" />
			</Overload>
		</KeyWord>
		<KeyWord name="puttext" />
		<KeyWord name="putw" />
		<KeyWord name="pword" />
		<KeyWord name="qsort" func="yes">
			<Overload retVal="void" >
				<Param name="void *base" />
				<Param name="size_t nmemb" />
				<Param name="size_t size" />
			</Overload>
		</KeyWord>
		<KeyWord name="queue" />
		<KeyWord name="quiet_NaN" />
		<KeyWord name="radix" />
		<KeyWord name="raise" func="yes">
			<Overload retVal="int" >
				<Param name="int sig" />
			</Overload>
		</KeyWord>
		<KeyWord name="rand" func="yes">
			<Overload retVal="int" >
				<Param name="void" />
			</Overload>
		</KeyWord>
		<KeyWord name="randbrd" />
		<KeyWord name="randbwr" />
		<KeyWord name="random" />
		<KeyWord name="randomize" />
		<KeyWord name="random_access_iterator" />
		<KeyWord name="random_shuffle" />
		<KeyWord name="raw_storage_iterator" />
		<KeyWord name="rbegin" />
		<KeyWord name="rdbuf" />
		<KeyWord name="rdstate" />
		<KeyWord name="read" func="yes">
			<Overload retVal="int" >
				<Param name="int filedes" />
				<Param name="void *buf" />
				<Param name="unsigned int nbyte" />
			</Overload>
		</KeyWord>
		<KeyWord name="readdir" func="yes">
			<Overload retVal="struct dirent *" >
				<Param name="DIR *dirp" />
			</Overload>
		</KeyWord>
		<KeyWord name="readsome" />
		<KeyWord name="real" />
		<KeyWord name="realloc" func="yes">
			<Overload retVal="void" >
				<Param name="void *ptr" />
				<Param name="size_t size" />
			</Overload>
		</KeyWord>
		<KeyWord name="rectangle" />
		<KeyWord name="reference" />
		<KeyWord name="reflexpr" />
		<KeyWord name="register" />
		<KeyWord name="registerbgidriver" />
		<KeyWord name="registerbgifont" />
		<KeyWord name="registerfarbgidriver" />
		<KeyWord name="registerfarbgifont" />
		<KeyWord name="register_callback" />
		<KeyWord name="reinterpret_cast" />
		<KeyWord name="release" />
		<KeyWord name="remove" func="yes">
			<Overload retVal="int" >
				<Param name="const char *filename" />
			</Overload>
		</KeyWord>
		<KeyWord name="remove_copy" />
		<KeyWord name="remove_copy_if" />
		<KeyWord name="remove_if" />
		<KeyWord name="rename" func="yes">
			<Overload retVal="int" >
				<Param name="const char * old" />
				<Param name="const char *new" />
			</Overload>
		</KeyWord>
		<KeyWord name="rend" />
		<KeyWord name="replace" />
		<KeyWord name="replace_copy" />
		<KeyWord name="replace_copy_if" />
		<KeyWord name="replace_if" />
		<KeyWord name="requires" />
		<KeyWord name="reserve" />
		<KeyWord name="reset" />
		<KeyWord name="resetiosflag" />
		<KeyWord name="resize" />
		<KeyWord name="restorecrtmode" />
		<KeyWord name="return" />
		<KeyWord name="return_temporary_buffer" />
		<KeyWord name="reverse" />
		<KeyWord name="reverse_bidirectional_iterator" />
		<KeyWord name="reverse_copy" />
		<KeyWord name="reverse_iterator" />
		<KeyWord name="rewind" func="yes">
			<Overload retVal="void" >
				<Param name="FILE *stream" />
			</Overload>
		</KeyWord>
		<KeyWord name="rewinddir" func="yes">
			<Overload retVal="void" >
				<Param name="DIR *dirp" />
			</Overload>
		</KeyWord>
		<KeyWord name="rfind" />
		<KeyWord name="right" />
		<KeyWord name="rmdir" func="yes">
			<Overload retVal="int" >
				<Param name="const char *path" />
			</Overload>
		</KeyWord>
		<KeyWord name="rmtmp" />
		<KeyWord name="rotate" />
		<KeyWord name="rotate_copy" />
		<KeyWord name="round_error" />
		<KeyWord name="round_style" />
		<KeyWord name="sbrk" />
		<KeyWord name="sbumpc" />
		<KeyWord name="sb_type" />
		<KeyWord name="scanf" func="yes">
			<Overload retVal="int" >
				<Param name="const char *format" />
				<Param name="..." />
			</Overload>
		</KeyWord>
		<KeyWord name="scan_is" />
		<KeyWord name="scan_not" />
		<KeyWord name="scientific" />
		<KeyWord name="search" />
		<KeyWord name="searchpath" />
		<KeyWord name="search_n" />
		<KeyWord name="sector" />
		<KeyWord name="seekdir" />
		<KeyWord name="seekg" />
		<KeyWord name="seekoff" />
		<KeyWord name="seekp" />
		<KeyWord name="seekpos" />
		<KeyWord name="segread" />
		<KeyWord name="sentry" />
		<KeyWord name="set" />
		<KeyWord name="setactivepage" />
		<KeyWord name="setallpalette" />
		<KeyWord name="setaspectratio" />
		<KeyWord name="setbase" />
		<KeyWord name="setbkcolor" />
		<KeyWord name="setblock" />
		<KeyWord name="setbuf" func="yes">
			<Overload retVal="void" >
				<Param name="FILE *stream" />
				<Param name="char *buf" />
			</Overload>
		</KeyWord>
		<KeyWord name="setcbrk" />
		<KeyWord name="setcolor" />
		<KeyWord name="setdate" />
		<KeyWord name="setdisk" />
		<KeyWord name="setdta" />
		<KeyWord name="setf" />
		<KeyWord name="setfill" />
		<KeyWord name="setfillpattern" />
		<KeyWord name="setfillstyle" />
		<KeyWord name="setftime" />
		<KeyWord name="setg" />
		<KeyWord name="setgraphbufsize" />
		<KeyWord name="setgraphmode" />
		<KeyWord name="setiosflag" />
		<KeyWord name="setjmp" func="yes">
			<Overload retVal="int" >
				<Param name="jmp_buf env" />
			</Overload>
		</KeyWord>
		<KeyWord name="setlinestyle" />
		<KeyWord name="setlocale" func="yes">
			<Overload retVal="char *" >
				<Param name="int category" />
				<Param name="const char *locale" />
			</Overload>
		</KeyWord>
		<KeyWord name="setmem" />
		<KeyWord name="setmode" />
		<KeyWord name="setp" />
		<KeyWord name="setpalette" />
		<KeyWord name="setprecision" />
		<KeyWord name="setrgbpalette" />
		<KeyWord name="setstate" />
		<KeyWord name="settextjustify" />
		<KeyWord name="settextstyle" />
		<KeyWord name="settime" />
		<KeyWord name="setusercharsize" />
		<KeyWord name="setvbuf" func="yes">
			<Overload retVal="int" >
				<Param name="FILE *stream" />
				<Param name="char *buf" />
				<Param name="int mode" />
				<Param name="size_t size" />
			</Overload>
		</KeyWord>
		<KeyWord name="setvect" />
		<KeyWord name="setverify" />
		<KeyWord name="setviewport" />
		<KeyWord name="setvisualpage" />
		<KeyWord name="setw" />
		<KeyWord name="setwritemode" />
		<KeyWord name="set_difference" />
		<KeyWord name="set_intersection" />
		<KeyWord name="set_new_handler" />
		<KeyWord name="set_symmetric_difference" />
		<KeyWord name="set_union" />
		<KeyWord name="sgetc" />
		<KeyWord name="sgetn" />
		<KeyWord name="short" />
		<KeyWord name="showbase" />
		<KeyWord name="showmanyc" />
		<KeyWord name="showpoint" />
		<KeyWord name="showpos" />
		<KeyWord name="signal" func="yes">
			<Overload retVal="void" >
				<Param name="int sig" />
				<Param name="void *func(int sig)" />
			</Overload>
		</KeyWord>
		<KeyWord name="signaling_NaN" />
		<KeyWord name="signed" />
		<KeyWord name="sin" func="yes">
			<Overload retVal="double" >
				<Param name="double x" />
			</Overload>
		</KeyWord>
		<KeyWord name="sinh" func="yes">
			<Overload retVal="double" >
				<Param name="double x" />
			</Overload>
		</KeyWord>
		<KeyWord name="sinhl" />
		<KeyWord name="sinl" />
		<KeyWord name="size" />
		<KeyWord name="sizeof" />
		<KeyWord name="size_t" />
		<KeyWord name="size_type" />
		<KeyWord name="skipws" />
		<KeyWord name="sleep" func="yes">
			<Overload retVal="unsigned int" >
				<Param name="unsigned int seconds" />
			</Overload>
		</KeyWord>
		<KeyWord name="snextc" />
		<KeyWord name="sopen" />
		<KeyWord name="sort" />
		<KeyWord name="sort_heap" />
		<KeyWord name="sound" />
		<KeyWord name="spawnl" />
		<KeyWord name="spawnle" />
		<KeyWord name="spawnlp" />
		<KeyWord name="spawnlpe" />
		<KeyWord name="spawnv" />
		<KeyWord name="spawnve" />
		<KeyWord name="spawnvp" />
		<KeyWord name="spawnvpe" />
		<KeyWord name="splice" />
		<KeyWord name="sprintf" func="yes">
			<Overload retVal="int" >
				<Param name="char *s" />
				<Param name="const char *format" />
				<Param name="..." />
			</Overload>
		</KeyWord>
		<KeyWord name="sputbackc" />
		<KeyWord name="sputc" />
		<KeyWord name="sputn" />
		<KeyWord name="sqrt" func="yes">
			<Overload retVal="double" >
				<Param name="double x" />
			</Overload>
		</KeyWord>
		<KeyWord name="sqrtl" />
		<KeyWord name="srand" func="yes">
			<Overload retVal="void" >
				<Param name="unsigned int seed" />
			</Overload>
		</KeyWord>
		<KeyWord name="sscanf" func="yes">
			<Overload retVal="int" >
				<Param name="const char *s" />
				<Param name="const char *format" />
				<Param name="..." />
			</Overload>
		</KeyWord>
		<KeyWord name="ssize_t" />
		<KeyWord name="stable_partition" />
		<KeyWord name="stable_sort" />
		<KeyWord name="stack" />
		<KeyWord name="stackavail" />
		<KeyWord name="stat" func="yes">
			<Overload retVal="int" >
				<Param name="const char *path" />
				<Param name="struct stat *buf" />
			</Overload>
		</KeyWord>
		<KeyWord name="state" />
		<KeyWord name="state_type" />
		<KeyWord name="static" />
		<KeyWord name="static_assert" />
		<KeyWord name="static_cast" />
		<KeyWord name="stime" />
		<KeyWord name="stpcpy" />
		<KeyWord name="str" />
		<KeyWord name="strcat" func="yes">
			<Overload retVal="char *" >
				<Param name="char *s1" />
				<Param name="const char *s2" />
			</Overload>
		</KeyWord>
		<KeyWord name="strchr" func="yes">
			<Overload retVal="char *" >
				<Param name="const char *s" />
				<Param name="int c" />
			</Overload>
		</KeyWord>
		<KeyWord name="strcmp" func="yes">
			<Overload retVal="int" >
				<Param name="const char *s1" />
				<Param name="const char *s2" />
			</Overload>
		</KeyWord>
		<KeyWord name="strcmpi" />
		<KeyWord name="strcoll" func="yes">
			<Overload retVal="int" >
				<Param name="const char *s1" />
				<Param name="const char *s2" />
			</Overload>
		</KeyWord>
		<KeyWord name="strcpy" func="yes">
			<Overload retVal="char *" >
				<Param name="char *s1" />
				<Param name="const char *s2" />
			</Overload>
		</KeyWord>
		<KeyWord name="strcspn" func="yes">
			<Overload retVal="size_t" >
				<Param name="const char *s1" />
				<Param name="const char *s2" />
			</Overload>
		</KeyWord>
		<KeyWord name="strdup" />
		<KeyWord name="streambuf" />
		<KeyWord name="streambuf_type" />
		<KeyWord name="strerror" func="yes">
			<Overload retVal="char *" >
				<Param name="int errnum" />
			</Overload>
		</KeyWord>
		<KeyWord name="strftime" func="yes">
			<Overload retVal="size_t *" >
				<Param name="char *s" />
				<Param name="size_t maxsize" />
				<Param name="const char *format" />
				<Param name="const struct tm *timeptr" />
			</Overload>
		</KeyWord>
		<KeyWord name="stricmp" />
		<KeyWord name="string" />
		<KeyWord name="stringbuf" />
		<KeyWord name="stringstream" />
		<KeyWord name="string_type" />
		<KeyWord name="strlen" func="yes">
			<Overload retVal="size_t" >
				<Param name="const char *s" />
			</Overload>
		</KeyWord>
		<KeyWord name="strlwr" />
		<KeyWord name="strncat" func="yes">
			<Overload retVal="char *" >
				<Param name="char *s1" />
				<Param name="const char *s2" />
				<Param name="size_t n" />
			</Overload>
		</KeyWord>
		<KeyWord name="strncmp" func="yes">
			<Overload retVal="int" >
				<Param name="const char *s1" />
				<Param name="const char *s2" />
				<Param name="size_t n" />
			</Overload>
		</KeyWord>
		<KeyWord name="strncmpi" />
		<KeyWord name="strncpy" func="yes">
			<Overload retVal="char *" >
				<Param name="char *s1" />
				<Param name="const char *s2" />
				<Param name="size_t n" />
			</Overload>
		</KeyWord>
		<KeyWord name="strnicmp" />
		<KeyWord name="strnset" />
		<KeyWord name="strpbrk" func="yes">
			<Overload retVal="char *" >
				<Param name="const char *s1" />
				<Param name="const char *s2" />
			</Overload>
		</KeyWord>
		<KeyWord name="strrchr" func="yes">
			<Overload retVal="char *" >
				<Param name="const char *s" />
				<Param name="int c" />
			</Overload>
		</KeyWord>
		<KeyWord name="strrev" />
		<KeyWord name="strset" />
		<KeyWord name="strspn" func="yes">
			<Overload retVal="size_t" >
				<Param name="const char *s1" />
				<Param name="const char *s2" />
			</Overload>
		</KeyWord>
		<KeyWord name="strstr" func="yes">
			<Overload retVal="char *" >
				<Param name="const char *s1" />
				<Param name="const char *s2" />
			</Overload>
		</KeyWord>
		<KeyWord name="strtod" func="yes">
			<Overload retVal="double" >
				<Param name="const char *nptr" />
				<Param name="char **endptr" />
			</Overload>
		</KeyWord>
		<KeyWord name="strtok" func="yes">
			<Overload retVal="char *" >
				<Param name="char *s1" />
				<Param name="const char *s2" />
			</Overload>
		</KeyWord>
		<KeyWord name="strtol" func="yes">
			<Overload retVal="long int" >
				<Param name="const char *nptr" />
				<Param name="char **endptr" />
				<Param name="int base" />
			</Overload>
		</KeyWord>
		<KeyWord name="strtoul" func="yes">
			<Overload retVal="unsigned long int" >
				<Param name="const char *nptr" />
				<Param name="char **endptr" />
				<Param name="int base" />
			</Overload>
		</KeyWord>
		<KeyWord name="struct" />
		<KeyWord name="strupr" />
		<KeyWord name="strxfrm" func="yes">
			<Overload retVal="size_t" >
				<Param name="char *s1" />
				<Param name="const char *s2" />
				<Param name="size_t n" />
			</Overload>
		</KeyWord>
		<KeyWord name="substr" />
		<KeyWord name="sungetc" />
		<KeyWord name="swab" />
		<KeyWord name="swap" />
		<KeyWord name="swap_ranges" />
		<KeyWord name="switch" />
		<KeyWord name="sync" />
		<KeyWord name="sync_with_stdio" />
		<KeyWord name="synchronized" />
		<KeyWord name="system" func="yes">
			<Overload retVal="int" >
				<Param name="const char *string" />
			</Overload>
		</KeyWord>
		<KeyWord name="table" />
		<KeyWord name="tan" func="yes">
			<Overload retVal="double" >
				<Param name="double x" />
			</Overload>
		</KeyWord>
		<KeyWord name="tanh" func="yes">
			<Overload retVal="double" >
				<Param name="double x" />
			</Overload>
		</KeyWord>
		<KeyWord name="tanhl" />
		<KeyWord name="tanl" />
		<KeyWord name="tell" />
		<KeyWord name="tellg" />
		<KeyWord name="tellp" />
		<KeyWord name="template" />
		<KeyWord name="tempnam" />
		<KeyWord name="test" />
		<KeyWord name="textattr" />
		<KeyWord name="textbackground" />
		<KeyWord name="textcolor" />
		<KeyWord name="textheight" />
		<KeyWord name="textmode" />
		<KeyWord name="textwidth" />
		<KeyWord name="this" />
		<KeyWord name="thousands_sep" />
		<KeyWord name="thread" />
		<KeyWord name="thread2" />
		<KeyWord name="thread_local" />
		<KeyWord name="throw" />
		<KeyWord name="tie" />
		<KeyWord name="time" func="yes">
			<Overload retVal="time_t" >
				<Param name="time_t *timer" />
			</Overload>
		</KeyWord>
		<KeyWord name="time_t" />
		<KeyWord name="times" func="yes">
			<Overload retVal="clock_t" >
				<Param name="struct tms *buffer" />
			</Overload>
		</KeyWord>
		<KeyWord name="tinyness_before" />
		<KeyWord name="tmpfile" func="yes">
			<Overload retVal="FILE *" >
				<Param name="void" />
			</Overload>
		</KeyWord>
		<KeyWord name="tmpnam" func="yes">
			<Overload retVal="char *" >
				<Param name="char *s" />
			</Overload>
		</KeyWord>
		<KeyWord name="toascii" />
		<KeyWord name="tolower" func="yes">
			<Overload retVal="int" >
				<Param name="int c" />
			</Overload>
		</KeyWord>
		<KeyWord name="top" />
		<KeyWord name="toupper" func="yes">
			<Overload retVal="int" >
				<Param name="int c" />
			</Overload>
		</KeyWord>
		<KeyWord name="to_char_type" />
		<KeyWord name="to_int_type" />
		<KeyWord name="to_string" />
		<KeyWord name="to_ulong" />
		<KeyWord name="traits" />
		<KeyWord name="traits_type" />
		<KeyWord name="transform" />
		<KeyWord name="traps" />
		<KeyWord name="true" />
		<KeyWord name="truename" />
		<KeyWord name="try" />
		<KeyWord name="typedef" />
		<KeyWord name="typeid" />
		<KeyWord name="typename" />
		<KeyWord name="tzset" func="yes">
			<Overload retVal="void" >
				<Param name="void" />
			</Overload>
		</KeyWord>
		<KeyWord name="uflow" />
		<KeyWord name="uint16_t" />
		<KeyWord name="uint32_t" />
		<KeyWord name="uint64_t" />
		<KeyWord name="uint8_t" />
		<KeyWord name="uint_fast16_t" />
		<KeyWord name="uint_fast32_t" />
		<KeyWord name="uint_fast64_t" />
		<KeyWord name="uint_fast8_t" />
		<KeyWord name="uint_least16_t" />
		<KeyWord name="uint_least32_t" />
		<KeyWord name="uint_least64_t" />
		<KeyWord name="uint_least8_t" />
		<KeyWord name="uintmax_t" />
		<KeyWord name="uintptr_t" />
		<KeyWord name="ultoa" />
		<KeyWord name="umask" func="yes">
			<Overload retVal="mode_t" >
				<Param name="mode_t cmask" />
			</Overload>
		</KeyWord>
		<KeyWord name="unary_function" />
		<KeyWord name="unary_negate" />
		<KeyWord name="underflow" />
		<KeyWord name="unget" />
		<KeyWord name="ungetc" func="yes">
			<Overload retVal="int" >
				<Param name="int c" />
				<Param name="FILE *stream" />
			</Overload>
		</KeyWord>
		<KeyWord name="ungetch" />
		<KeyWord name="uninitialized_copy" />
		<KeyWord name="uninitialized_fill" />
		<KeyWord name="uninitialized_fill_n" />
		<KeyWord name="union" />
		<KeyWord name="unique" />
		<KeyWord name="unique_copy" />
		<KeyWord name="unitbuf" />
		<KeyWord name="unixtodos" />
		<KeyWord name="unlink" func="yes">
			<Overload retVal="int" >
				<Param name="const char *path" />
			</Overload>
		</KeyWord>
		<KeyWord name="unlock" />
		<KeyWord name="unsetf" />
		<KeyWord name="unsigned" />
		<KeyWord name="uppercase" />
		<KeyWord name="upper_bound" />
		<KeyWord name="using" />
		<KeyWord name="utime" func="yes">
			<Overload retVal="int" >
				<Param name="const char *path" />
				<Param name="const struct utimbuf *times" />
			</Overload>
		</KeyWord>
		<KeyWord name="value_comp" />
		<KeyWord name="value_type" />
		<KeyWord name="va_arg" func="yes">
			<Overload retVal="type" >
				<Param name="va_list ap" />
				<Param name="type" />
			</Overload>
		</KeyWord>
		<KeyWord name="va_end" func="yes">
			<Overload retVal="void" >
				<Param name="va_list ap" />
			</Overload>
		</KeyWord>
		<KeyWord name="va_list" />
		<KeyWord name="va_start" func="yes">
			<Overload retVal="void" >
				<Param name="va_list ap" />
				<Param name="parmN" />
			</Overload>
		</KeyWord>
		<KeyWord name="vector" />
		<KeyWord name="vfprintf" func="yes">
			<Overload retVal="int" >
				<Param name="FILE *stream" />
				<Param name="const char *format" />
				<Param name="va_list arg" />
			</Overload>
		</KeyWord>
		<KeyWord name="vfscanf" />
		<KeyWord name="virtual" />
		<KeyWord name="void" />
		<KeyWord name="void*" />
		<KeyWord name="volatile" />
		<KeyWord name="vprintf" func="yes">
			<Overload retVal="int" >
				<Param name="const char *format" />
				<Param name="va_list arg" />
			</Overload>
		</KeyWord>
		<KeyWord name="vscanf" />
		<KeyWord name="vsprintf" func="yes">
			<Overload retVal="int" >
				<Param name="char *s" />
				<Param name="const char *format" />
				<Param name="va_list arg" />
			</Overload>
		</KeyWord>
		<KeyWord name="vsscanf" />
		<KeyWord name="wcerr" />
		<KeyWord name="wchar_t" />
		<KeyWord name="wcin" />
		<KeyWord name="wclog" />
		<KeyWord name="wcout" />
		<KeyWord name="wcstombs" func="yes">
			<Overload retVal="size_t" >
				<Param name="char *s" />
				<Param name="const wchar_t *pwcs" />
				<Param name="size_t n" />
			</Overload>
		</KeyWord>
		<KeyWord name="wctomb" func="yes">
			<Overload retVal="int" >
				<Param name="char *s" />
				<Param name="wchar_t wchar" />
			</Overload>
		</KeyWord>
		<KeyWord name="wfilebuf" />
		<KeyWord name="wfstream" />
		<KeyWord name="what" />
		<KeyWord name="wherex" />
		<KeyWord name="wherey" />
		<KeyWord name="which_open_mode" />
		<KeyWord name="while" />
		<KeyWord name="widen" />
		<KeyWord name="width" />
		<KeyWord name="wifstream" />
		<KeyWord name="window" />
		<KeyWord name="wios" />
		<KeyWord name="wistream" />
		<KeyWord name="wistringstream" />
		<KeyWord name="wofstream" />
		<KeyWord name="wostream" />
		<KeyWord name="wostringstream" />
		<KeyWord name="write" func="yes">
			<Overload retVal="int" >
				<Param name="int filedes" />
				<Param name="const void *buf" />
				<Param name="unsigned int nbyte" />
			</Overload>
		</KeyWord>
		<KeyWord name="ws" />
		<KeyWord name="wstreambuf" />
		<KeyWord name="wstring" />
		<KeyWord name="wstringbuf" />
		<KeyWord name="wstringstream" />
		<KeyWord name="xalloc" />
		<KeyWord name="xor" />
		<KeyWord name="xor_eq" />
		<KeyWord name="xsgetn" />
		<KeyWord name="xsputn" />
		<KeyWord name="_atold" />
		<KeyWord name="_bios_disk" />
		<KeyWord name="_bios_equiplist" />
		<KeyWord name="_bios_keybrd" />
		<KeyWord name="_bios_memsize" />
		<KeyWord name="_bios_printer" />
		<KeyWord name="_bios_serialcom" />
		<KeyWord name="_bios_timeofday" />
		<KeyWord name="_cexit" />
		<KeyWord name="_chain_intr" />
		<KeyWord name="_chdrive" />
		<KeyWord name="_chmod" />
		<KeyWord name="_clear87" />
		<KeyWord name="_close" />
		<KeyWord name="_control87" />
		<KeyWord name="_creat" />
		<KeyWord name="_c_exit" />
		<KeyWord name="_disable" />
		<KeyWord name="_dos_allocmem" />
		<KeyWord name="_dos_close" />
		<KeyWord name="_dos_creat" />
		<KeyWord name="_dos_creatnew" />
		<KeyWord name="_dos_findfirst" />
		<KeyWord name="_dos_findnext" />
		<KeyWord name="_dos_freemem" />
		<KeyWord name="_dos_getdate" />
		<KeyWord name="_dos_getdiskfree" />
		<KeyWord name="_dos_getdrive" />
		<KeyWord name="_dos_getfileattr" />
		<KeyWord name="_dos_getftime" />
		<KeyWord name="_dos_gettime" />
		<KeyWord name="_dos_getvect" />
		<KeyWord name="_dos_keep" />
		<KeyWord name="_dos_open" />
		<KeyWord name="_dos_read" />
		<KeyWord name="_dos_setblock" />
		<KeyWord name="_dos_setdate" />
		<KeyWord name="_dos_setdrive" />
		<KeyWord name="_dos_setfileattr" />
		<KeyWord name="_dos_setftime" />
		<KeyWord name="_dos_settime" />
		<KeyWord name="_dos_setvect" />
		<KeyWord name="_dos_write" />
		<KeyWord name="_enable" />
		<KeyWord name="_exit" func="yes">
			<Overload retVal="void" >
				<Param name="int status" />
			</Overload>
		</KeyWord>
		<KeyWord name="_fmemccpy" />
		<KeyWord name="_fmemchr" />
		<KeyWord name="_fmemcmp" />
		<KeyWord name="_fmemcpy" />
		<KeyWord name="_fmemicmp" />
		<KeyWord name="_fmemset" />
		<KeyWord name="_fpreset" />
		<KeyWord name="_fsopen" />
		<KeyWord name="_fstrcat" />
		<KeyWord name="_fstrchr" />
		<KeyWord name="_fstrcmp" />
		<KeyWord name="_fstrcpy" />
		<KeyWord name="_fstrcspn" />
		<KeyWord name="_fstrdup" />
		<KeyWord name="_fstricmp" />
		<KeyWord name="_fstrlen" />
		<KeyWord name="_fstrlwr" />
		<KeyWord name="_fstrncat" />
		<KeyWord name="_fstrncmp" />
		<KeyWord name="_fstrncpy" />
		<KeyWord name="_fstrnicmp" />
		<KeyWord name="_fstrnset" />
		<KeyWord name="_fstrpbrk" />
		<KeyWord name="_fstrrchr" />
		<KeyWord name="_fstrrev" />
		<KeyWord name="_fstrset" />
		<KeyWord name="_fstrspn" />
		<KeyWord name="_fstrstr" />
		<KeyWord name="_fstrtok" />
		<KeyWord name="_fstrupr" />
		<KeyWord name="_fullpath" />
		<KeyWord name="_getdcwd" />
		<KeyWord name="_getdrive" />
		<KeyWord name="_graphfreemem" />
		<KeyWord name="_graphgetmem" />
		<KeyWord name="_harderr" />
		<KeyWord name="_hardresume" />
		<KeyWord name="_hardretn" />
		<KeyWord name="_lrotl" />
		<KeyWord name="_lrotr" />
		<KeyWord name="_makepath" />
		<KeyWord name="_matherrl" />
		<KeyWord name="_open" />
		<KeyWord name="_OvrInitEms" />
		<KeyWord name="_OvrInitExt" />
		<KeyWord name="_read" />
		<KeyWord name="_rotl" />
		<KeyWord name="_rotr" />
		<KeyWord name="_searchenv" />
		<KeyWord name="_setcursortype" />
		<KeyWord name="_splitpath" />
		<KeyWord name="_status87" />
		<KeyWord name="_strdate" />
		<KeyWord name="_strerror" />
		<KeyWord name="_strtime" />
		<KeyWord name="_strtold" />
		<KeyWord name="_tolower" />
		<KeyWord name="_toupper" />
		<KeyWord name="_write" />
		<KeyWord name="__asm" />
		<KeyWord name="__based1" />
		<KeyWord name="__cdecl" />
		<KeyWord name="__declspec" />
		<KeyWord name="__emit__" />
		<KeyWord name="__except" />
		<KeyWord name="__fastcall" />
		<KeyWord name="__finally" />
		<KeyWord name="__inline" />
		<KeyWord name="__int16" />
		<KeyWord name="__int32" />
		<KeyWord name="__int64" />
		<KeyWord name="__int8" />
		<KeyWord name="__leave" />
		<KeyWord name="__multiple_inheritance" />
		<KeyWord name="__single_inheritance" />
		<KeyWord name="__stdcall" />
		<KeyWord name="__try" />
		<KeyWord name="__virtual_inheritance" />
		<KeyWord name="~sentry" />
	</AutoComplete>
</NotepadPlus>