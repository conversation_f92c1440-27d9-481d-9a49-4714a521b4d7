@echo off
title Development Environment Setup

echo ========================================
echo Development Environment Setup
echo ========================================
echo.

echo Setting up Python environment...
if exist "Development\Python\python.exe" (
    echo Configuring Python...
    
    REM Create pip configuration
    if not exist "Development\Python\Scripts" mkdir "Development\Python\Scripts"
    
    REM Download get-pip.py
    echo Downloading pip installer...
    curl -L -o "Development\Python\get-pip.py" "https://bootstrap.pypa.io/get-pip.py"
    
    REM Install pip
    if exist "Development\Python\get-pip.py" (
        echo Installing pip...
        "Development\Python\python.exe" "Development\Python\get-pip.py"
    )
    
    echo Python setup complete!
) else (
    echo Python not found. Please run the downloader first.
)

echo.
echo Setting up Git...
if exist "Development\git-portable.exe" (
    echo Extracting Git Portable...
    "Development\git-portable.exe" -o"Development\Git" -y
    echo Git setup complete!
) else (
    echo Git Portable not found. Please run the downloader first.
)

echo.
echo Creating development shortcuts...

REM Create VS Code shortcut
if exist "Editors\VSCode\Code.exe" (
    echo Creating VS Code shortcut...
    powershell -Command "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%USERPROFILE%\Desktop\VS Code.lnk'); $Shortcut.TargetPath = '%CD%\Editors\VSCode\Code.exe'; $Shortcut.WorkingDirectory = '%CD%\Editors\VSCode'; $Shortcut.Save()"
)

REM Create Python shortcut
if exist "Development\Python\python.exe" (
    echo Creating Python shortcut...
    powershell -Command "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%USERPROFILE%\Desktop\Python.lnk'); $Shortcut.TargetPath = '%CD%\Development\Python\python.exe'; $Shortcut.WorkingDirectory = '%CD%\Development\Python'; $Shortcut.Save()"
)

REM Create Git Bash shortcut
if exist "Development\Git\git-bash.exe" (
    echo Creating Git Bash shortcut...
    powershell -Command "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%USERPROFILE%\Desktop\Git Bash.lnk'); $Shortcut.TargetPath = '%CD%\Development\Git\git-bash.exe'; $Shortcut.WorkingDirectory = '%CD%\Development\Git'; $Shortcut.Save()"
)

echo.
echo Creating environment variables script...
echo @echo off > set_dev_env.bat
echo REM Development Environment Variables >> set_dev_env.bat
echo set PATH=%CD%\Development\Python;%CD%\Development\Git\bin;%CD%\Utilities;%%PATH%% >> set_dev_env.bat
echo set PYTHONPATH=%CD%\Development\Python >> set_dev_env.bat
echo echo Development environment configured! >> set_dev_env.bat
echo echo Python: %CD%\Development\Python\python.exe >> set_dev_env.bat
echo echo Git: %CD%\Development\Git\bin\git.exe >> set_dev_env.bat
echo echo VS Code: %CD%\Editors\VSCode\Code.exe >> set_dev_env.bat

echo.
echo ========================================
echo Development Environment Ready!
echo ========================================
echo.
echo Available tools:
echo   - VS Code: Editors\VSCode\Code.exe
echo   - Python: Development\Python\python.exe
echo   - Git: Development\Git\bin\git.exe
echo   - Git Bash: Development\Git\git-bash.exe
echo.
echo Desktop shortcuts created for easy access.
echo Run 'set_dev_env.bat' to configure environment variables.
echo.
pause
