<?xml version="1.0" encoding="UTF-8" ?>
<NotepadPlus>
	<AutoComplete>
		<Environment ignoreCase="no" startFunc="(" stopFunc=")" paramSeparator="," terminal=";" />
		<KeyWord name="-BGCOLOR=>" />
		<KeyWord name="-absolute=>" />
		<KeyWord name="-action=>" />
		<KeyWord name="-align=>" />
		<KeyWord name="-alt=>" />
		<KeyWord name="-anchor=>" />
		<KeyWord name="-author=>" />
		<KeyWord name="-background=>" />
		<KeyWord name="-base=>" />
		<KeyWord name="-bgcolor=>" />
		<KeyWord name="-border=>" />
		<KeyWord name="-borderwidth=>" />
		<KeyWord name="-boundary=>" />
		<KeyWord name="-cellpadding=>" />
		<KeyWord name="-cellspacing=>" />
		<KeyWord name="-checked=>" />
		<KeyWord name="-class=>" />
		<KeyWord name="-code=>" />
		<KeyWord name="-colheader=>" />
		<KeyWord name="-color=>" />
		<KeyWord name="-colspan=>" />
		<KeyWord name="-columns=>" />
		<KeyWord name="-command=>" />
		<KeyWord name="-compact=>" />
		<KeyWord name="-content=>" />
		<KeyWord name="-cookie=>" />
		<KeyWord name="-default=>" />
		<KeyWord name="-defaultextension=>" />
		<KeyWord name="-domain=>" />
		<KeyWord name="-dtd=>" />
		<KeyWord name="-expand=>" />
		<KeyWord name="-expires=>" />
		<KeyWord name="-face=>" />
		<KeyWord name="-filetypes=>" />
		<KeyWord name="-fill=>" />
		<KeyWord name="-foreground=>" />
		<KeyWord name="-full=>" />
		<KeyWord name="-head=>" />
		<KeyWord name="-height=>" />
		<KeyWord name="-href=>" />
		<KeyWord name="-initialdir=>" />
		<KeyWord name="-initialfile=>" />
		<KeyWord name="-justification=>" />
		<KeyWord name="-label=>" />
		<KeyWord name="-labels=>" />
		<KeyWord name="-language=>" />
		<KeyWord name="-leftmargin=>" />
		<KeyWord name="-linebreak=>" />
		<KeyWord name="-location=>" />
		<KeyWord name="-maxlength=>" />
		<KeyWord name="-meta=>" />
		<KeyWord name="-method=>" />
		<KeyWord name="-msg=>" />
		<KeyWord name="-multiple=>" />
		<KeyWord name="-name=>" />
		<KeyWord name="-nolabels=>" />
		<KeyWord name="-nph=>" />
		<KeyWord name="-on=>" />
		<KeyWord name="-onBlur=>" />
		<KeyWord name="-onChange=>" />
		<KeyWord name="-onClick=>" />
		<KeyWord name="-onFocus=>" />
		<KeyWord name="-onLoad=>" />
		<KeyWord name="-onMouseOut=>" />
		<KeyWord name="-onMouseOver=>" />
		<KeyWord name="-onSelect=>" />
		<KeyWord name="-onSubmit=>" />
		<KeyWord name="-onUnload=>" />
		<KeyWord name="-override=>" />
		<KeyWord name="-padx=>" />
		<KeyWord name="-pady=>" />
		<KeyWord name="-path=>" />
		<KeyWord name="-path_info=>" />
		<KeyWord name="-query=>" />
		<KeyWord name="-query_string=>" />
		<KeyWord name="-refresh=>" />
		<KeyWord name="-rel=>" />
		<KeyWord name="-relative=>" />
		<KeyWord name="-relief=>" />
		<KeyWord name="-rowheader=>" />
		<KeyWord name="-rows=>" />
		<KeyWord name="-rowspan=>" />
		<KeyWord name="-script=>" />
		<KeyWord name="-secure=>" />
		<KeyWord name="-selected=>" />
		<KeyWord name="-side=>" />
		<KeyWord name="-size=>" />
		<KeyWord name="-src=>" />
		<KeyWord name="-status=>" />
		<KeyWord name="-style=>" />
		<KeyWord name="-target=>" />
		<KeyWord name="-text=>" />
		<KeyWord name="-textvariable=>" />
		<KeyWord name="-title=>" />
		<KeyWord name="-topmargin=>" />
		<KeyWord name="-type=>" />
		<KeyWord name="-uri=>" />
		<KeyWord name="-url=>" />
		<KeyWord name="-valign=>" />
		<KeyWord name="-value=>" />
		<KeyWord name="-values=>" />
		<KeyWord name="-width=>" />
		<KeyWord name="-wrap=>" />
		<KeyWord name="-xbase=>" />
		<KeyWord name="ADJUST" />
		<KeyWord name="ARGV" />
		<KeyWord name="AUTOLOAD" />
		<KeyWord name="Accept" />
		<KeyWord name="BEGIN" />
		<KeyWord name="Balloon" />
		<KeyWord name="Button" />
		<KeyWord name="CHECK" />
		<KeyWord name="CORE" />
		<KeyWord name="DATA" />
		<KeyWord name="DESTROY" />
		<KeyWord name="END" />
		<KeyWord name="Entry" />
		<KeyWord name="Frame" />
		<KeyWord name="INIT" />
		<KeyWord name="Label" />
		<KeyWord name="MainLoop" />
		<KeyWord name="Radiobutton" />
		<KeyWord name="STDERR" />
		<KeyWord name="STDIN" />
		<KeyWord name="STDOUT" />
		<KeyWord name="SUPER" />
		<KeyWord name="Tr" />
		<KeyWord name="UNITCHECK" />
		<KeyWord name="UNIVERSAL" />
		<KeyWord name="__DATA__" />
		<KeyWord name="__END__" />
		<KeyWord name="__FILE__" func="yes"><Overload retVal="" descr="the name of the current source file"><Param name="" /></Overload></KeyWord>
		<KeyWord name="__LINE__" func="yes"><Overload retVal="" descr="the current source line number"><Param name="" /></Overload></KeyWord>
		<KeyWord name="__PACKAGE__" func="yes"><Overload retVal="" descr="the current package"><Param name="" /></Overload></KeyWord>
		<KeyWord name="__SUB__" func="yes"><Overload retVal="" descr="the current subroutine, or undef if not in a subroutine"><Param name="" /></Overload></KeyWord>
		<KeyWord name="abs" func="yes"><Overload retVal="" descr="absolute value function"><Param name="VALUE" /></Overload><Overload retVal="" descr="absolute value function"><Param name="" /></Overload></KeyWord>
		<KeyWord name="accept" func="yes"><Overload retVal="" descr="accept an incoming socket connect"><Param name="NEWSOCKET" /><Param name="GENERICSOCKET" /></Overload></KeyWord>
		<KeyWord name="address" />
		<KeyWord name="alarm" func="yes"><Overload retVal="" descr="schedule a SIGALRM"><Param name="SECONDS" /></Overload><Overload retVal="" descr="schedule a SIGALRM"><Param name="" /></Overload></KeyWord>
		<KeyWord name="and" />
		<KeyWord name="atan2" func="yes"><Overload retVal="" descr="arctangent of Y/X in the range -PI to PI"><Param name="Y" /><Param name="X" /></Overload></KeyWord>
		<KeyWord name="attach" />
		<KeyWord name="attributes" />
		<KeyWord name="auth_type" />
		<KeyWord name="autoEscape" />
		<KeyWord name="autodie" />
		<KeyWord name="autouse" />
		<KeyWord name="base" />
		<KeyWord name="bigfloat" />
		<KeyWord name="bigint" />
		<KeyWord name="bignum" />
		<KeyWord name="bigrat" />
		<KeyWord name="bind" func="yes"><Overload retVal="" descr="binds an address to a socket"><Param name="SOCKET" /><Param name="NAME" /></Overload></KeyWord>
		<KeyWord name="binmode" func="yes"><Overload retVal="" descr="prepare binary files for I/O"><Param name="FILEHANDLE" /></Overload><Overload retVal="" descr="prepare binary files for I/O"><Param name="FILEHANDLE" /><Param name="LAYER" /></Overload></KeyWord>
		<KeyWord name="bless" func="yes"><Overload retVal="" descr="create an object"><Param name="REF" /></Overload><Overload retVal="" descr="create an object"><Param name="REF" /><Param name="CLASSNAME" /></Overload></KeyWord>
		<KeyWord name="blib" />
		<KeyWord name="br" />
		<KeyWord name="break" func="yes"><Overload retVal="" descr="break out of a given block"><Param name="" /></Overload></KeyWord>
		<KeyWord name="builtin" />
		<KeyWord name="button" />
		<KeyWord name="bytes" />
		<KeyWord name="caller" func="yes"><Overload retVal="" descr="get context of the current subroutine call"><Param name="EXPR" /></Overload><Overload retVal="" descr="get context of the current subroutine call"><Param name="" /></Overload></KeyWord>
		<KeyWord name="caption" />
		<KeyWord name="catch" func="yes"><Overload retVal="" descr="exception handling: catch an exception"><Param name="BLOCK" /></Overload></KeyWord>
		<KeyWord name="charnames" />
		<KeyWord name="chdir" func="yes"><Overload retVal="" descr="change your current working directory"><Param name="DIRHANDLE" /></Overload><Overload retVal="" descr="change your current working directory"><Param name="EXPR" /></Overload><Overload retVal="" descr="change your current working directory"><Param name="FILEHANDLE" /></Overload><Overload retVal="" descr="change your current working directory"><Param name="" /></Overload></KeyWord>
		<KeyWord name="checkbox" />
		<KeyWord name="checkbox_group" />
		<KeyWord name="chmod" func="yes"><Overload retVal="" descr="changes the permissions on a list of files"><Param name="LIST" /></Overload></KeyWord>
		<KeyWord name="chomp" func="yes"><Overload retVal="" descr="remove a trailing record separator from a string"><Param name="VARIABLE" /></Overload><Overload retVal="" descr="remove a trailing record separator from a string"><Param name="( LIST )" /></Overload><Overload retVal="" descr="remove a trailing record separator from a string"><Param name="" /></Overload></KeyWord>
		<KeyWord name="chop" func="yes"><Overload retVal="" descr="remove the last character from a string"><Param name="VARIABLE" /></Overload><Overload retVal="" descr="remove the last character from a string"><Param name="( LIST )" /></Overload><Overload retVal="" descr="remove the last character from a string"><Param name="" /></Overload></KeyWord>
		<KeyWord name="chown" func="yes"><Overload retVal="" descr="change the ownership on a list of files"><Param name="LIST" /></Overload></KeyWord>
		<KeyWord name="chr" func="yes"><Overload retVal="" descr="get character this number represents"><Param name="NUMBER" /></Overload><Overload retVal="" descr="get character this number represents"><Param name="" /></Overload></KeyWord>
		<KeyWord name="chroot" func="yes"><Overload retVal="" descr="make directory new root for path lookups"><Param name="FILENAME" /></Overload><Overload retVal="" descr="make directory new root for path lookups"><Param name="" /></Overload></KeyWord>
		<KeyWord name="class" />
		<KeyWord name="close" func="yes"><Overload retVal="" descr="close file (or pipe or socket) handle"><Param name="FILEHANDLE" /></Overload><Overload retVal="" descr="close file (or pipe or socket) handle"><Param name="" /></Overload></KeyWord>
		<KeyWord name="closedir" func="yes"><Overload retVal="" descr="close directory handle"><Param name="DIRHANDLE" /></Overload></KeyWord>
		<KeyWord name="cmp" />
		<KeyWord name="compile" />
		<KeyWord name="configure" />
		<KeyWord name="connect" func="yes"><Overload retVal="" descr="connect to a remote socket"><Param name="SOCKET" /><Param name="NAME" /></Overload></KeyWord>
		<KeyWord name="constant" />
		<KeyWord name="continue" func="yes"><Overload retVal="" descr="optional trailing block in a while or foreach"><Param name="BLOCK" /></Overload><Overload retVal="" descr="optional trailing block in a while or foreach"><Param name="" /></Overload></KeyWord>
		<KeyWord name="cookie" />
		<KeyWord name="cos" func="yes"><Overload retVal="" descr="cosine function"><Param name="EXPR" /></Overload><Overload retVal="" descr="cosine function"><Param name="" /></Overload></KeyWord>
		<KeyWord name="crypt" func="yes"><Overload retVal="" descr="one-way passwd-style encryption"><Param name="PLAINTEXT" /><Param name="SALT" /></Overload></KeyWord>
		<KeyWord name="dbmclose" func="yes"><Overload retVal="" descr="breaks binding on a tied dbm file"><Param name="HASH" /></Overload></KeyWord>
		<KeyWord name="dbmopen" func="yes"><Overload retVal="" descr="create binding on a tied dbm file"><Param name="HASH" /><Param name="DBNAME" /><Param name="MASK" /></Overload></KeyWord>
		<KeyWord name="default" />
		<KeyWord name="defaults" />
		<KeyWord name="defer" func="yes"><Overload retVal="" descr="provides a section of code which runs at a later time during scope exit"><Param name="BLOCK" /></Overload></KeyWord>
		<KeyWord name="defined" func="yes"><Overload retVal="" descr="test whether a value, variable, or function is defined"><Param name="EXPR" /></Overload><Overload retVal="" descr="test whether a value, variable, or function is defined"><Param name="" /></Overload></KeyWord>
		<KeyWord name="deiconify" />
		<KeyWord name="delete" func="yes"><Overload retVal="" descr="deletes a value from a hash"><Param name="EXPR" /></Overload></KeyWord>
		<KeyWord name="delete_all" />
		<KeyWord name="deprecate" />
		<KeyWord name="diagnostics" />
		<KeyWord name="die" func="yes"><Overload retVal="" descr="raise an exception or bail out"><Param name="LIST" /></Overload></KeyWord>
		<KeyWord name="div" />
		<KeyWord name="do" func="yes"><Overload retVal="" descr="turn a BLOCK into a TERM"><Param name="BLOCK" /></Overload><Overload retVal="" descr="turn a BLOCK into a TERM"><Param name="EXPR" /></Overload></KeyWord>
		<KeyWord name="dump" func="yes"><Overload retVal="" descr="create an immediate core dump"><Param name="EXPR" /></Overload><Overload retVal="" descr="create an immediate core dump"><Param name="LABEL" /></Overload><Overload retVal="" descr="create an immediate core dump"><Param name="" /></Overload></KeyWord>
		<KeyWord name="each" func="yes"><Overload retVal="" descr="retrieve the next key/value pair from a hash"><Param name="ARRAY" /></Overload><Overload retVal="" descr="retrieve the next key/value pair from a hash"><Param name="HASH" /></Overload></KeyWord>
		<KeyWord name="else" />
		<KeyWord name="elsif" />
		<KeyWord name="em" />
		<KeyWord name="encoding" />
		<KeyWord name="end" />
		<KeyWord name="end_h1" />
		<KeyWord name="end_html" />
		<KeyWord name="end_table" />
		<KeyWord name="end_ul" />
		<KeyWord name="endform" />
		<KeyWord name="endgrent" func="yes"><Overload retVal="" descr="be done using group file"><Param name="" /></Overload></KeyWord>
		<KeyWord name="endhostent" func="yes"><Overload retVal="" descr="be done using hosts file"><Param name="" /></Overload></KeyWord>
		<KeyWord name="endif" />
		<KeyWord name="endnetent" func="yes"><Overload retVal="" descr="be done using networks file"><Param name="" /></Overload></KeyWord>
		<KeyWord name="endprotoent" func="yes"><Overload retVal="" descr="be done using protocols file"><Param name="" /></Overload></KeyWord>
		<KeyWord name="endpwent" func="yes"><Overload retVal="" descr="be done using passwd file"><Param name="" /></Overload></KeyWord>
		<KeyWord name="endservent" func="yes"><Overload retVal="" descr="be done using services file"><Param name="" /></Overload></KeyWord>
		<KeyWord name="eof" func="yes"><Overload retVal="" descr="test a filehandle for its end"><Param name="()" /></Overload><Overload retVal="" descr="test a filehandle for its end"><Param name="FILEHANDLE" /></Overload><Overload retVal="" descr="test a filehandle for its end"><Param name="" /></Overload></KeyWord>
		<KeyWord name="eq" />
		<KeyWord name="escape" />
		<KeyWord name="escape_HTML" />
		<KeyWord name="eval" func="yes"><Overload retVal="" descr="catch exceptions or compile and run code"><Param name="BLOCK" /></Overload><Overload retVal="" descr="catch exceptions or compile and run code"><Param name="EXPR" /></Overload><Overload retVal="" descr="catch exceptions or compile and run code"><Param name="" /></Overload></KeyWord>
		<KeyWord name="evalbytes" func="yes"><Overload retVal="" descr="similar to string eval, but intend to parse a bytestream"><Param name="EXPR" /></Overload><Overload retVal="" descr="similar to string eval, but intend to parse a bytestream"><Param name="" /></Overload></KeyWord>
		<KeyWord name="exec" func="yes"><Overload retVal="" descr="abandon this program to run another"><Param name="LIST" /></Overload><Overload retVal="" descr="abandon this program to run another"><Param name="PROGRAM LIST" /></Overload></KeyWord>
		<KeyWord name="exists" func="yes"><Overload retVal="" descr="test whether a hash key is present"><Param name="EXPR" /></Overload></KeyWord>
		<KeyWord name="exit" func="yes"><Overload retVal="" descr="terminate this program"><Param name="EXPR" /></Overload><Overload retVal="" descr="terminate this program"><Param name="" /></Overload></KeyWord>
		<KeyWord name="exp" func="yes"><Overload retVal="" descr="raise e to a power"><Param name="EXPR" /></Overload><Overload retVal="" descr="raise e to a power"><Param name="" /></Overload></KeyWord>
		<KeyWord name="experimental" />
		<KeyWord name="fc" func="yes"><Overload retVal="" descr="return casefolded version of a string"><Param name="EXPR" /></Overload><Overload retVal="" descr="return casefolded version of a string"><Param name="" /></Overload></KeyWord>
		<KeyWord name="fcntl" func="yes"><Overload retVal="" descr="file control system call"><Param name="FILEHANDLE" /><Param name="FUNCTION" /><Param name="SCALAR" /></Overload></KeyWord>
		<KeyWord name="feature" />
		<KeyWord name="field" />
		<KeyWord name="fields" />
		<KeyWord name="filefield" />
		<KeyWord name="fileno" func="yes"><Overload retVal="" descr="return file descriptor from filehandle"><Param name="DIRHANDLE" /></Overload><Overload retVal="" descr="return file descriptor from filehandle"><Param name="FILEHANDLE" /></Overload></KeyWord>
		<KeyWord name="filetest" />
		<KeyWord name="finally" func="yes"><Overload retVal="" descr="exception handling: runs block after try/catch"><Param name="BLOCK" /></Overload></KeyWord>
		<KeyWord name="flock" func="yes"><Overload retVal="" descr="lock an entire file with an advisory lock"><Param name="FILEHANDLE" /><Param name="OPERATION" /></Overload></KeyWord>
		<KeyWord name="font" />
		<KeyWord name="for" />
		<KeyWord name="foreach" />
		<KeyWord name="fork" func="yes"><Overload retVal="" descr="create a new process just like this one"><Param name="" /></Overload></KeyWord>
		<KeyWord name="format" func="yes"><Overload retVal="" descr="declare a picture format with use by the write() function"><Param name="" /></Overload></KeyWord>
		<KeyWord name="formline" func="yes"><Overload retVal="" descr="internal function used for formats"><Param name="PICTURE" /><Param name="LIST" /></Overload></KeyWord>
		<KeyWord name="ge" />
		<KeyWord name="geometry" />
		<KeyWord name="getc" func="yes"><Overload retVal="" descr="get the next character from the filehandle"><Param name="FILEHANDLE" /></Overload><Overload retVal="" descr="get the next character from the filehandle"><Param name="" /></Overload></KeyWord>
		<KeyWord name="getgrent" func="yes"><Overload retVal="" descr="get next group record"><Param name="" /></Overload></KeyWord>
		<KeyWord name="getgrgid" func="yes"><Overload retVal="" descr="get group record given group user ID"><Param name="GID" /></Overload></KeyWord>
		<KeyWord name="getgrnam" func="yes"><Overload retVal="" descr="get group record given group name"><Param name="NAME" /></Overload></KeyWord>
		<KeyWord name="getgrname" />
		<KeyWord name="gethostbyaddr" func="yes"><Overload retVal="" descr="get host record given its address"><Param name="ADDR" /><Param name="ADDRTYPE" /></Overload></KeyWord>
		<KeyWord name="gethostbyname" func="yes"><Overload retVal="" descr="get host record given name"><Param name="NAME" /></Overload></KeyWord>
		<KeyWord name="gethostent" func="yes"><Overload retVal="" descr="get next hosts record"><Param name="" /></Overload></KeyWord>
		<KeyWord name="getlogin" func="yes"><Overload retVal="" descr="return who logged in at this tty"><Param name="" /></Overload></KeyWord>
		<KeyWord name="getnetbyaddr" func="yes"><Overload retVal="" descr="get network record given its address"><Param name="ADDR" /><Param name="ADDRTYPE" /></Overload></KeyWord>
		<KeyWord name="getnetbyname" func="yes"><Overload retVal="" descr="get networks record given name"><Param name="NAME" /></Overload></KeyWord>
		<KeyWord name="getnetent" func="yes"><Overload retVal="" descr="get next networks record"><Param name="" /></Overload></KeyWord>
		<KeyWord name="getpeername" func="yes"><Overload retVal="" descr="find the other end of a socket connection"><Param name="SOCKET" /></Overload></KeyWord>
		<KeyWord name="getpgrp" func="yes"><Overload retVal="" descr="get process group"><Param name="PID" /></Overload></KeyWord>
		<KeyWord name="getppid" func="yes"><Overload retVal="" descr="get parent process ID"><Param name="" /></Overload></KeyWord>
		<KeyWord name="getpriority" func="yes"><Overload retVal="" descr="get current nice value"><Param name="WHICH" /><Param name="WHO" /></Overload></KeyWord>
		<KeyWord name="getprotobyname" func="yes"><Overload retVal="" descr="get protocol record given name"><Param name="NAME" /></Overload></KeyWord>
		<KeyWord name="getprotobynumber" func="yes"><Overload retVal="" descr="get protocol record numeric protocol"><Param name="NUMBER" /></Overload></KeyWord>
		<KeyWord name="getprotoent" func="yes"><Overload retVal="" descr="get next protocols record"><Param name="" /></Overload></KeyWord>
		<KeyWord name="getpwent" func="yes"><Overload retVal="" descr="get next passwd record"><Param name="" /></Overload></KeyWord>
		<KeyWord name="getpwnam" func="yes"><Overload retVal="" descr="get passwd record given user login name"><Param name="NAME" /></Overload></KeyWord>
		<KeyWord name="getpwuid" func="yes"><Overload retVal="" descr="get passwd record given user ID"><Param name="UID" /></Overload></KeyWord>
		<KeyWord name="getservbyname" func="yes"><Overload retVal="" descr="get services record given its name"><Param name="NAME" /><Param name="PROTO" /></Overload></KeyWord>
		<KeyWord name="getservbyport" func="yes"><Overload retVal="" descr="get services record given numeric port"><Param name="PORT" /><Param name="PROTO" /></Overload></KeyWord>
		<KeyWord name="getservent" func="yes"><Overload retVal="" descr="get next services record"><Param name="" /></Overload></KeyWord>
		<KeyWord name="getsockname" func="yes"><Overload retVal="" descr="retrieve the sockaddr for a given socket"><Param name="SOCKET" /></Overload></KeyWord>
		<KeyWord name="getsockopt" func="yes"><Overload retVal="" descr="get socket options on a given socket"><Param name="SOCKET" /><Param name="LEVEL" /><Param name="OPTNAME" /></Overload></KeyWord>
		<KeyWord name="given" />
		<KeyWord name="glob" func="yes"><Overload retVal="" descr="expand filenames using wildcards"><Param name="EXPR" /></Overload><Overload retVal="" descr="expand filenames using wildcards"><Param name="" /></Overload></KeyWord>
		<KeyWord name="gmtime" func="yes"><Overload retVal="" descr="convert UNIX time into record or string using Greenwich time"><Param name="EXPR" /></Overload><Overload retVal="" descr="convert UNIX time into record or string using Greenwich time"><Param name="" /></Overload></KeyWord>
		<KeyWord name="goto" func="yes"><Overload retVal="" descr="create spaghetti code"><Param name="&amp;NAME" /></Overload><Overload retVal="" descr="create spaghetti code"><Param name="EXPR" /></Overload><Overload retVal="" descr="create spaghetti code"><Param name="LABEL" /></Overload></KeyWord>
		<KeyWord name="grep" func="yes"><Overload retVal="" descr="locate elements in a list test true against a given criterion"><Param name="BLOCK LIST" /></Overload><Overload retVal="" descr="locate elements in a list test true against a given criterion"><Param name="EXPR" /><Param name="LIST" /></Overload></KeyWord>
		<KeyWord name="groove" />
		<KeyWord name="gt" />
		<KeyWord name="h1" />
		<KeyWord name="h2" />
		<KeyWord name="h3" />
		<KeyWord name="h4" />
		<KeyWord name="h5" />
		<KeyWord name="header" />
		<KeyWord name="hex" func="yes"><Overload retVal="" descr="convert a hexadecimal string to a number"><Param name="EXPR" /></Overload><Overload retVal="" descr="convert a hexadecimal string to a number"><Param name="" /></Overload></KeyWord>
		<KeyWord name="hidden" />
		<KeyWord name="hr" />
		<KeyWord name="http" />
		<KeyWord name="https" />
		<KeyWord name="if" />
		<KeyWord name="image_button" />
		<KeyWord name="img" />
		<KeyWord name="import" func="yes"><Overload retVal="" descr="patch a module's namespace into your own"><Param name="LIST" /></Overload></KeyWord>
		<KeyWord name="index" func="yes"><Overload retVal="" descr="find a substring within a string"><Param name="STR" /><Param name="SUBSTR" /></Overload><Overload retVal="" descr="find a substring within a string"><Param name="STR" /><Param name="SUBSTR" /><Param name="POSITION" /></Overload></KeyWord>
		<KeyWord name="insert" />
		<KeyWord name="int" func="yes"><Overload retVal="" descr="get the integer portion of a number"><Param name="EXPR" /></Overload><Overload retVal="" descr="get the integer portion of a number"><Param name="" /></Overload></KeyWord>
		<KeyWord name="integer" />
		<KeyWord name="ioctl" func="yes"><Overload retVal="" descr="system-dependent device control system call"><Param name="FILEHANDLE" /><Param name="FUNCTION" /><Param name="SCALAR" /></Overload></KeyWord>
		<KeyWord name="isa" />
		<KeyWord name="isindex" />
		<KeyWord name="join" func="yes"><Overload retVal="" descr="join a list into a string using a separator"><Param name="EXPR" /><Param name="LIST" /></Overload></KeyWord>
		<KeyWord name="keys" func="yes"><Overload retVal="" descr="retrieve list of indices from a hash"><Param name="ARRAY" /></Overload><Overload retVal="" descr="retrieve list of indices from a hash"><Param name="HASH" /></Overload></KeyWord>
		<KeyWord name="kill" func="yes"><Overload retVal="" descr="send a signal to a process or process group"><Param name="SIGNAL" /></Overload><Overload retVal="" descr="send a signal to a process or process group"><Param name="SIGNAL" /><Param name="LIST" /></Overload></KeyWord>
		<KeyWord name="last" func="yes"><Overload retVal="" descr="exit a block prematurely"><Param name="EXPR" /></Overload><Overload retVal="" descr="exit a block prematurely"><Param name="LABEL" /></Overload><Overload retVal="" descr="exit a block prematurely"><Param name="" /></Overload></KeyWord>
		<KeyWord name="lc" func="yes"><Overload retVal="" descr="return lower-case version of a string"><Param name="EXPR" /></Overload><Overload retVal="" descr="return lower-case version of a string"><Param name="" /></Overload></KeyWord>
		<KeyWord name="lcfirst" func="yes"><Overload retVal="" descr="return a string with just the next letter in lower case"><Param name="EXPR" /></Overload><Overload retVal="" descr="return a string with just the next letter in lower case"><Param name="" /></Overload></KeyWord>
		<KeyWord name="le" />
		<KeyWord name="length" func="yes"><Overload retVal="" descr="return the number of characters in a string"><Param name="EXPR" /></Overload><Overload retVal="" descr="return the number of characters in a string"><Param name="" /></Overload></KeyWord>
		<KeyWord name="less" />
		<KeyWord name="li" />
		<KeyWord name="lib" />
		<KeyWord name="link" func="yes"><Overload retVal="" descr="create a hard link in the filesystem"><Param name="OLDFILE" /><Param name="NEWFILE" /></Overload></KeyWord>
		<KeyWord name="listen" func="yes"><Overload retVal="" descr="register your socket as a server"><Param name="SOCKET" /><Param name="QUEUESIZE" /></Overload></KeyWord>
		<KeyWord name="local" func="yes"><Overload retVal="" descr="create a temporary value for a global variable (dynamic scoping)"><Param name="EXPR" /></Overload></KeyWord>
		<KeyWord name="locale" />
		<KeyWord name="localtime" func="yes"><Overload retVal="" descr="convert UNIX time into record or string using local time"><Param name="EXPR" /></Overload><Overload retVal="" descr="convert UNIX time into record or string using local time"><Param name="" /></Overload></KeyWord>
		<KeyWord name="lock" func="yes"><Overload retVal="" descr="get a thread lock on a variable, subroutine, or method"><Param name="THING" /></Overload></KeyWord>
		<KeyWord name="log" func="yes"><Overload retVal="" descr="retrieve the natural logarithm for a number"><Param name="EXPR" /></Overload><Overload retVal="" descr="retrieve the natural logarithm for a number"><Param name="" /></Overload></KeyWord>
		<KeyWord name="lstat" func="yes"><Overload retVal="" descr="stat a symbolic link"><Param name="DIRHANDLE" /></Overload><Overload retVal="" descr="stat a symbolic link"><Param name="EXPR" /></Overload><Overload retVal="" descr="stat a symbolic link"><Param name="FILEHANDLE" /></Overload><Overload retVal="" descr="stat a symbolic link"><Param name="" /></Overload></KeyWord>
		<KeyWord name="lt" />
		<KeyWord name="m" func="yes"><Overload retVal="" descr="quotes string as substitution regex"><Param name="" /></Overload></KeyWord>
		<KeyWord name="map" func="yes"><Overload retVal="" descr="apply a change to a list to get back a new list with the changes"><Param name="BLOCK LIST" /></Overload><Overload retVal="" descr="apply a change to a list to get back a new list with the changes"><Param name="EXPR" /><Param name="LIST" /></Overload></KeyWord>
		<KeyWord name="maxsize" />
		<KeyWord name="method" />
		<KeyWord name="minsize" />
		<KeyWord name="mkdir" func="yes"><Overload retVal="" descr="create a directory"><Param name="FILENAME" /></Overload><Overload retVal="" descr="create a directory"><Param name="FILENAME" /><Param name="MODE" /></Overload><Overload retVal="" descr="create a directory"><Param name="" /></Overload></KeyWord>
		<KeyWord name="mro" />
		<KeyWord name="msgctl" func="yes"><Overload retVal="" descr="SysV IPC message control operations"><Param name="ID" /><Param name="CMD" /><Param name="ARG" /></Overload></KeyWord>
		<KeyWord name="msgget" func="yes"><Overload retVal="" descr="get SysV IPC message queue"><Param name="KEY" /><Param name="FLAGS" /></Overload></KeyWord>
		<KeyWord name="msgrcv" func="yes"><Overload retVal="" descr="receive a SysV IPC message from a message queue"><Param name="ID" /><Param name="VAR" /><Param name="SIZE" /><Param name="TYPE,FLAGS" /></Overload></KeyWord>
		<KeyWord name="msgsnd" func="yes"><Overload retVal="" descr="send a SysV IPC message to a message queue"><Param name="ID" /><Param name="MSG" /><Param name="FLAGS" /></Overload></KeyWord>
		<KeyWord name="multipart_end" />
		<KeyWord name="multipart_init" />
		<KeyWord name="multipart_start" />
		<KeyWord name="my" func="yes"><Overload retVal="" descr="declare and assign a local variable (lexical scoping)"><Param name="TYPE VARLIST" /></Overload><Overload retVal="" descr="declare and assign a local variable (lexical scoping)"><Param name="TYPE VARLIST : ATTRS" /></Overload><Overload retVal="" descr="declare and assign a local variable (lexical scoping)"><Param name="VARLIST" /></Overload><Overload retVal="" descr="declare and assign a local variable (lexical scoping)"><Param name="VARLIST : ATTRS" /></Overload></KeyWord>
		<KeyWord name="ne" />
		<KeyWord name="next" func="yes"><Overload retVal="" descr="iterate a block prematurely"><Param name="EXPR" /></Overload><Overload retVal="" descr="iterate a block prematurely"><Param name="LABEL" /></Overload><Overload retVal="" descr="iterate a block prematurely"><Param name="" /></Overload></KeyWord>
		<KeyWord name="no" func="yes"><Overload retVal="" descr="unimport some module symbols or semantics at compile time"><Param name="MODULE" /></Overload><Overload retVal="" descr="unimport some module symbols or semantics at compile time"><Param name="MODULE LIST" /></Overload><Overload retVal="" descr="unimport some module symbols or semantics at compile time"><Param name="MODULE VERSION" /></Overload><Overload retVal="" descr="unimport some module symbols or semantics at compile time"><Param name="MODULE VERSION LIST" /></Overload><Overload retVal="" descr="unimport some module symbols or semantics at compile time"><Param name="VERSION" /></Overload></KeyWord>
		<KeyWord name="not" />
		<KeyWord name="oct" func="yes"><Overload retVal="" descr="convert a string to an octal number"><Param name="EXPR" /></Overload><Overload retVal="" descr="convert a string to an octal number"><Param name="" /></Overload></KeyWord>
		<KeyWord name="ok" />
		<KeyWord name="ol" />
		<KeyWord name="open" func="yes"><Overload retVal="" descr="open a file, pipe, or descriptor"><Param name="FILEHANDLE" /></Overload><Overload retVal="" descr="open a file, pipe, or descriptor"><Param name="FILEHANDLE" /><Param name="EXPR" /></Overload><Overload retVal="" descr="open a file, pipe, or descriptor"><Param name="FILEHANDLE" /><Param name="MODE" /><Param name="EXPR" /></Overload><Overload retVal="" descr="open a file, pipe, or descriptor"><Param name="FILEHANDLE" /><Param name="MODE" /><Param name="EXPR" /><Param name="LIST" /></Overload><Overload retVal="" descr="open a file, pipe, or descriptor"><Param name="FILEHANDLE" /><Param name="MODE" /><Param name="REFERENCE" /></Overload></KeyWord>
		<KeyWord name="opendir" func="yes"><Overload retVal="" descr="open a directory"><Param name="DIRHANDLE" /><Param name="EXPR" /></Overload></KeyWord>
		<KeyWord name="ops" />
		<KeyWord name="or" />
		<KeyWord name="ord" func="yes"><Overload retVal="" descr="find a character's numeric representation"><Param name="EXPR" /></Overload><Overload retVal="" descr="find a character's numeric representation"><Param name="" /></Overload></KeyWord>
		<KeyWord name="order" />
		<KeyWord name="our" func="yes"><Overload retVal="" descr="declare and assign a package variable (lexical scoping)"><Param name="TYPE VARLIST" /></Overload><Overload retVal="" descr="declare and assign a package variable (lexical scoping)"><Param name="TYPE VARLIST : ATTRS" /></Overload><Overload retVal="" descr="declare and assign a package variable (lexical scoping)"><Param name="VARLIST" /></Overload><Overload retVal="" descr="declare and assign a package variable (lexical scoping)"><Param name="VARLIST : ATTRS" /></Overload></KeyWord>
		<KeyWord name="overload" />
		<KeyWord name="overloading" />
		<KeyWord name="pack" func="yes"><Overload retVal="" descr="convert a list into a binary representation"><Param name="TEMPLATE" /><Param name="LIST" /></Overload></KeyWord>
		<KeyWord name="package" func="yes"><Overload retVal="" descr="declare a separate global namespace"><Param name="NAMESPACE" /></Overload><Overload retVal="" descr="declare a separate global namespace"><Param name="NAMESPACE BLOCK" /></Overload><Overload retVal="" descr="declare a separate global namespace"><Param name="NAMESPACE VERSION" /></Overload><Overload retVal="" descr="declare a separate global namespace"><Param name="NAMESPACE VERSION BLOCK" /></Overload></KeyWord>
		<KeyWord name="param" />
		<KeyWord name="param_fetch" />
		<KeyWord name="parent" />
		<KeyWord name="password_field" />
		<KeyWord name="path_info" />
		<KeyWord name="pipe" func="yes"><Overload retVal="" descr="open a pair of connected filehandles"><Param name="READHANDLE" /><Param name="WRITEHANDLE" /></Overload></KeyWord>
		<KeyWord name="pop" func="yes"><Overload retVal="" descr="remove the last element from an array and return it"><Param name="ARRAY" /></Overload><Overload retVal="" descr="remove the last element from an array and return it"><Param name="" /></Overload></KeyWord>
		<KeyWord name="popup_menu" />
		<KeyWord name="pos" func="yes"><Overload retVal="" descr="find or set the offset for the last/next m//g search"><Param name="SCALAR" /></Overload><Overload retVal="" descr="find or set the offset for the last/next m//g search"><Param name="" /></Overload></KeyWord>
		<KeyWord name="pre" />
		<KeyWord name="print" func="yes"><Overload retVal="" descr="output a list to a filehandle"><Param name="FILEHANDLE" /></Overload><Overload retVal="" descr="output a list to a filehandle"><Param name="FILEHANDLE LIST" /></Overload><Overload retVal="" descr="output a list to a filehandle"><Param name="LIST" /></Overload><Overload retVal="" descr="output a list to a filehandle"><Param name="" /></Overload></KeyWord>
		<KeyWord name="printf" func="yes"><Overload retVal="" descr="output a formatted list to a filehandle"><Param name="FILEHANDLE" /></Overload><Overload retVal="" descr="output a formatted list to a filehandle"><Param name="FILEHANDLE FORMAT" /><Param name="LIST" /></Overload><Overload retVal="" descr="output a formatted list to a filehandle"><Param name="FORMAT" /><Param name="LIST" /></Overload><Overload retVal="" descr="output a formatted list to a filehandle"><Param name="" /></Overload></KeyWord>
		<KeyWord name="prototype" func="yes"><Overload retVal="" descr="get the prototype (if any) of a subroutine"><Param name="FUNCTION" /></Overload><Overload retVal="" descr="get the prototype (if any) of a subroutine"><Param name="" /></Overload></KeyWord>
		<KeyWord name="push" func="yes"><Overload retVal="" descr="append one or more elements to an array"><Param name="ARRAY" /><Param name="LIST" /></Overload></KeyWord>
		<KeyWord name="q" func="yes"><Overload retVal="" descr="singly quote a string"><Param name="" /></Overload></KeyWord>
		<KeyWord name="qq" func="yes"><Overload retVal="" descr="doubly quote a string"><Param name="" /></Overload></KeyWord>
		<KeyWord name="qr" func="yes"><Overload retVal="" descr="quotes string as regular expression"><Param name="" /></Overload></KeyWord>
		<KeyWord name="query_string" />
		<KeyWord name="quotemeta" func="yes"><Overload retVal="" descr="quote regular expression magic characters"><Param name="EXPR" /></Overload><Overload retVal="" descr="quote regular expression magic characters"><Param name="" /></Overload></KeyWord>
		<KeyWord name="qw" func="yes"><Overload retVal="" descr="quote a list of words"><Param name="" /></Overload></KeyWord>
		<KeyWord name="qx" func="yes"><Overload retVal="" descr="backquote quote a string"><Param name="" /></Overload></KeyWord>
		<KeyWord name="radio_group" />
		<KeyWord name="raise" />
		<KeyWord name="rand" func="yes"><Overload retVal="" descr="retrieve the next pseudorandom number"><Param name="EXPR" /></Overload><Overload retVal="" descr="retrieve the next pseudorandom number"><Param name="" /></Overload></KeyWord>
		<KeyWord name="raw_cookie" />
		<KeyWord name="re" />
		<KeyWord name="read" func="yes"><Overload retVal="" descr="fixed-length buffered input from a filehandle"><Param name="FILEHANDLE" /><Param name="SCALAR" /><Param name="LENGTH" /></Overload><Overload retVal="" descr="fixed-length buffered input from a filehandle"><Param name="FILEHANDLE" /><Param name="SCALAR" /><Param name="LENGTH" /><Param name="OFFSET" /></Overload></KeyWord>
		<KeyWord name="readdir" func="yes"><Overload retVal="" descr="get a directory from a directory handle"><Param name="DIRHANDLE" /></Overload></KeyWord>
		<KeyWord name="readline" func="yes"><Overload retVal="" descr="fetch a record from a file"><Param name="EXPR" /></Overload><Overload retVal="" descr="fetch a record from a file"><Param name="" /></Overload></KeyWord>
		<KeyWord name="readlink" func="yes"><Overload retVal="" descr="determine where a symbolic link is pointing"><Param name="EXPR" /></Overload><Overload retVal="" descr="determine where a symbolic link is pointing"><Param name="" /></Overload></KeyWord>
		<KeyWord name="readpipe" func="yes"><Overload retVal="" descr="execute a system command and collect standard output"><Param name="EXPR" /></Overload><Overload retVal="" descr="execute a system command and collect standard output"><Param name="" /></Overload></KeyWord>
		<KeyWord name="recv" func="yes"><Overload retVal="" descr="receive a message over a Socket"><Param name="SOCKET" /><Param name="SCALAR" /><Param name="LENGTH" /><Param name="FLAGS" /></Overload></KeyWord>
		<KeyWord name="redirect" />
		<KeyWord name="redo" func="yes"><Overload retVal="" descr="start this loop iteration over again"><Param name="EXPR" /></Overload><Overload retVal="" descr="start this loop iteration over again"><Param name="LABEL" /></Overload><Overload retVal="" descr="start this loop iteration over again"><Param name="" /></Overload></KeyWord>
		<KeyWord name="ref" func="yes"><Overload retVal="" descr="find out the type of thing being referenced"><Param name="EXPR" /></Overload><Overload retVal="" descr="find out the type of thing being referenced"><Param name="" /></Overload></KeyWord>
		<KeyWord name="referer" />
		<KeyWord name="remote_addr" />
		<KeyWord name="remote_host" />
		<KeyWord name="remote_indent" />
		<KeyWord name="remote_user" />
		<KeyWord name="rename" func="yes"><Overload retVal="" descr="change a filename"><Param name="OLDNAME" /><Param name="NEWNAME" /></Overload></KeyWord>
		<KeyWord name="request_method" />
		<KeyWord name="require" func="yes"><Overload retVal="" descr="load in external functions from a library at runtime"><Param name="EXPR" /></Overload><Overload retVal="" descr="load in external functions from a library at runtime"><Param name="VERSION" /></Overload><Overload retVal="" descr="load in external functions from a library at runtime"><Param name="" /></Overload></KeyWord>
		<KeyWord name="reset" func="yes"><Overload retVal="" descr="clear all variables of a given name"><Param name="EXPR" /></Overload><Overload retVal="" descr="clear all variables of a given name"><Param name="" /></Overload></KeyWord>
		<KeyWord name="resizable" />
		<KeyWord name="return" func="yes"><Overload retVal="" descr="get out of a function early"><Param name="EXPR" /></Overload><Overload retVal="" descr="get out of a function early"><Param name="" /></Overload></KeyWord>
		<KeyWord name="reverse" func="yes"><Overload retVal="" descr="flip a string or a list"><Param name="LIST" /></Overload></KeyWord>
		<KeyWord name="rewinddir" func="yes"><Overload retVal="" descr="reset directory handle"><Param name="DIRHANDLE" /></Overload></KeyWord>
		<KeyWord name="rindex" func="yes"><Overload retVal="" descr="right-to-left substring search"><Param name="STR" /><Param name="SUBSTR" /></Overload><Overload retVal="" descr="right-to-left substring search"><Param name="STR" /><Param name="SUBSTR" /><Param name="POSITION" /></Overload></KeyWord>
		<KeyWord name="rmdir" func="yes"><Overload retVal="" descr="remove a directory"><Param name="FILENAME" /></Overload><Overload retVal="" descr="remove a directory"><Param name="" /></Overload></KeyWord>
		<KeyWord name="s" func="yes"><Overload retVal="" descr="quotes string as substitution regex"><Param name="" /></Overload></KeyWord>
		<KeyWord name="say" func="yes"><Overload retVal="" descr="output a list to a filehandle, appending a newline"><Param name="FILEHANDLE" /></Overload><Overload retVal="" descr="output a list to a filehandle, appending a newline"><Param name="FILEHANDLE LIST" /></Overload><Overload retVal="" descr="output a list to a filehandle, appending a newline"><Param name="LIST" /></Overload><Overload retVal="" descr="output a list to a filehandle, appending a newline"><Param name="" /></Overload></KeyWord>
		<KeyWord name="scalar" func="yes"><Overload retVal="" descr="force a scalar context"><Param name="EXPR" /></Overload></KeyWord>
		<KeyWord name="script_name" />
		<KeyWord name="scrolling_list" />
		<KeyWord name="seek" func="yes"><Overload retVal="" descr="reposition file pointer for random-access I/O"><Param name="FILEHANDLE" /><Param name="POSITION" /><Param name="WHENCE" /></Overload></KeyWord>
		<KeyWord name="seekdir" func="yes"><Overload retVal="" descr="reposition directory pointer"><Param name="DIRHANDLE" /><Param name="POS" /></Overload></KeyWord>
		<KeyWord name="select" func="yes"><Overload retVal="" descr="reset default output or do I/O multiplexing"><Param name="FILEHANDLE" /></Overload><Overload retVal="" descr="reset default output or do I/O multiplexing"><Param name="RBITS" /><Param name="WBITS" /><Param name="EBITS" /><Param name="TIMEOUT" /></Overload><Overload retVal="" descr="reset default output or do I/O multiplexing"><Param name="" /></Overload></KeyWord>
		<KeyWord name="self_url" />
		<KeyWord name="semctl" func="yes"><Overload retVal="" descr="SysV semaphore control operations"><Param name="ID" /><Param name="SEMNUM" /><Param name="CMD" /><Param name="ARG" /></Overload></KeyWord>
		<KeyWord name="semget" func="yes"><Overload retVal="" descr="get set of SysV semaphores"><Param name="KEY" /><Param name="NSEMS" /><Param name="FLAGS" /></Overload></KeyWord>
		<KeyWord name="semop" func="yes"><Overload retVal="" descr="SysV semaphore operations"><Param name="KEY" /><Param name="OPSTRING" /></Overload></KeyWord>
		<KeyWord name="send" func="yes"><Overload retVal="" descr="send a message over a socket"><Param name="SOCKET" /><Param name="MSG" /><Param name="FLAGS" /></Overload><Overload retVal="" descr="send a message over a socket"><Param name="SOCKET" /><Param name="MSG" /><Param name="FLAGS" /><Param name="TO" /></Overload></KeyWord>
		<KeyWord name="server_name" />
		<KeyWord name="server_port" />
		<KeyWord name="server_software" />
		<KeyWord name="set" />
		<KeyWord name="setgrent" func="yes"><Overload retVal="" descr="prepare group file for use"><Param name="" /></Overload></KeyWord>
		<KeyWord name="sethostent" func="yes"><Overload retVal="" descr="prepare hosts file for use"><Param name="STAYOPEN" /></Overload></KeyWord>
		<KeyWord name="setnetent" func="yes"><Overload retVal="" descr="prepare networks file for use"><Param name="STAYOPEN" /></Overload></KeyWord>
		<KeyWord name="setpgrp" func="yes"><Overload retVal="" descr="set the process group of a process"><Param name="PID" /><Param name="PGRP" /></Overload></KeyWord>
		<KeyWord name="setpriority" func="yes"><Overload retVal="" descr="set a process's nice value"><Param name="WHICH" /><Param name="WHO" /><Param name="PRIORITY" /></Overload></KeyWord>
		<KeyWord name="setprotoent" func="yes"><Overload retVal="" descr="prepare protocols file for use"><Param name="STAYOPEN" /></Overload></KeyWord>
		<KeyWord name="setpwent" func="yes"><Overload retVal="" descr="prepare passwd file for use"><Param name="" /></Overload></KeyWord>
		<KeyWord name="setservent" func="yes"><Overload retVal="" descr="prepare services file for use"><Param name="STAYOPEN" /></Overload></KeyWord>
		<KeyWord name="setsockopt" func="yes"><Overload retVal="" descr="set some socket options"><Param name="SOCKET" /><Param name="LEVEL" /><Param name="OPTNAME" /><Param name="OPTVAL" /></Overload></KeyWord>
		<KeyWord name="shift" func="yes"><Overload retVal="" descr="remove the first element of an array, and return it"><Param name="ARRAY" /></Overload><Overload retVal="" descr="remove the first element of an array, and return it"><Param name="" /></Overload></KeyWord>
		<KeyWord name="shmctl" func="yes"><Overload retVal="" descr="SysV shared memory operations"><Param name="ID" /><Param name="CMD" /><Param name="ARG" /></Overload></KeyWord>
		<KeyWord name="shmget" func="yes"><Overload retVal="" descr="get SysV shared memory segment identifier"><Param name="KEY" /><Param name="SIZE" /><Param name="FLAGS" /></Overload></KeyWord>
		<KeyWord name="shmread" func="yes"><Overload retVal="" descr="read SysV shared memory"><Param name="ID" /><Param name="VAR" /><Param name="POS" /><Param name="SIZE" /></Overload></KeyWord>
		<KeyWord name="shmwrite" func="yes"><Overload retVal="" descr="write SysV shared memory"><Param name="ID" /><Param name="STRING" /><Param name="POS" /><Param name="SIZE" /></Overload></KeyWord>
		<KeyWord name="shutdown" func="yes"><Overload retVal="" descr="close down just half of a socket connection"><Param name="SOCKET" /><Param name="HOW" /></Overload></KeyWord>
		<KeyWord name="sigtrap" />
		<KeyWord name="sin" func="yes"><Overload retVal="" descr="return the sine of a number"><Param name="EXPR" /></Overload><Overload retVal="" descr="return the sine of a number"><Param name="" /></Overload></KeyWord>
		<KeyWord name="size" />
		<KeyWord name="sleep" func="yes"><Overload retVal="" descr="block for some number of seconds"><Param name="EXPR" /></Overload><Overload retVal="" descr="block for some number of seconds"><Param name="" /></Overload></KeyWord>
		<KeyWord name="socket" func="yes"><Overload retVal="" descr="create a socket"><Param name="SOCKET" /><Param name="DOMAIN" /><Param name="TYPE" /><Param name="PROTOCOL" /></Overload></KeyWord>
		<KeyWord name="socketpair" func="yes"><Overload retVal="" descr="create a pair of sockets"><Param name="SOCKET1" /><Param name="SOCKET2" /><Param name="DOMAIN" /><Param name="TYPE,PROTOCOL" /></Overload></KeyWord>
		<KeyWord name="sort" func="yes"><Overload retVal="" descr="sort a list of values"><Param name="BLOCK LIST" /></Overload><Overload retVal="" descr="sort a list of values"><Param name="LIST" /></Overload><Overload retVal="" descr="sort a list of values"><Param name="SUBNAME LIST" /></Overload></KeyWord>
		<KeyWord name="span" />
		<KeyWord name="splice" func="yes"><Overload retVal="" descr="add or remove elements anywhere in an array"><Param name="ARRAY" /></Overload><Overload retVal="" descr="add or remove elements anywhere in an array"><Param name="ARRAY" /><Param name="OFFSET" /></Overload><Overload retVal="" descr="add or remove elements anywhere in an array"><Param name="ARRAY" /><Param name="OFFSET" /><Param name="LENGTH" /></Overload><Overload retVal="" descr="add or remove elements anywhere in an array"><Param name="ARRAY" /><Param name="OFFSET" /><Param name="LENGTH" /><Param name="LIST" /></Overload></KeyWord>
		<KeyWord name="split" func="yes"><Overload retVal="" descr="split up a string using a regexp delimiter"><Param name="/PATTERN/" /></Overload><Overload retVal="" descr="split up a string using a regexp delimiter"><Param name="/PATTERN/" /><Param name="EXPR" /></Overload><Overload retVal="" descr="split up a string using a regexp delimiter"><Param name="/PATTERN/" /><Param name="EXPR" /><Param name="LIMIT" /></Overload><Overload retVal="" descr="split up a string using a regexp delimiter"><Param name="" /></Overload></KeyWord>
		<KeyWord name="sprintf" func="yes"><Overload retVal="" descr="formatted print into a string"><Param name="FORMAT" /><Param name="LIST" /></Overload></KeyWord>
		<KeyWord name="sqrt" func="yes"><Overload retVal="" descr="square root function"><Param name="EXPR" /></Overload><Overload retVal="" descr="square root function"><Param name="" /></Overload></KeyWord>
		<KeyWord name="srand" func="yes"><Overload retVal="" descr="seed the random number generator"><Param name="EXPR" /></Overload><Overload retVal="" descr="seed the random number generator"><Param name="" /></Overload></KeyWord>
		<KeyWord name="stable" />
		<KeyWord name="start_form" />
		<KeyWord name="start_h1" />
		<KeyWord name="start_html" />
		<KeyWord name="start_multipart_form" />
		<KeyWord name="start_table" />
		<KeyWord name="start_ul" />
		<KeyWord name="startform" />
		<KeyWord name="stat" func="yes"><Overload retVal="" descr="get a file's status information"><Param name="DIRHANDLE" /></Overload><Overload retVal="" descr="get a file's status information"><Param name="EXPR" /></Overload><Overload retVal="" descr="get a file's status information"><Param name="FILEHANDLE" /></Overload><Overload retVal="" descr="get a file's status information"><Param name="" /></Overload></KeyWord>
		<KeyWord name="state" func="yes"><Overload retVal="" descr="declare and assign a persistent lexical variable"><Param name="TYPE VARLIST" /></Overload><Overload retVal="" descr="declare and assign a persistent lexical variable"><Param name="TYPE VARLIST : ATTRS" /></Overload><Overload retVal="" descr="declare and assign a persistent lexical variable"><Param name="VARLIST" /></Overload><Overload retVal="" descr="declare and assign a persistent lexical variable"><Param name="VARLIST : ATTRS" /></Overload></KeyWord>
		<KeyWord name="strict" />
		<KeyWord name="strong" />
		<KeyWord name="study" func="yes"><Overload retVal="" descr="no-op, formerly optimized input data for repeated searches"><Param name="SCALAR" /></Overload><Overload retVal="" descr="no-op, formerly optimized input data for repeated searches"><Param name="" /></Overload></KeyWord>
		<KeyWord name="sub" func="yes"><Overload retVal="" descr="declare a subroutine, possibly anonymously"><Param name="NAME (PROTO) : ATTRS BLOCK" /></Overload><Overload retVal="" descr="declare a subroutine, possibly anonymously"><Param name="NAME (PROTO) BLOCK" /></Overload><Overload retVal="" descr="declare a subroutine, possibly anonymously"><Param name="NAME : ATTRS BLOCK" /></Overload><Overload retVal="" descr="declare a subroutine, possibly anonymously"><Param name="NAME BLOCK" /></Overload></KeyWord>
		<KeyWord name="submit" />
		<KeyWord name="subs" />
		<KeyWord name="substr" func="yes"><Overload retVal="" descr="get or alter a portion of a string"><Param name="EXPR" /><Param name="OFFSET" /></Overload><Overload retVal="" descr="get or alter a portion of a string"><Param name="EXPR" /><Param name="OFFSET" /><Param name="LENGTH" /></Overload><Overload retVal="" descr="get or alter a portion of a string"><Param name="EXPR" /><Param name="OFFSET" /><Param name="LENGTH" /><Param name="REPLACEMENT" /></Overload></KeyWord>
		<KeyWord name="symlink" func="yes"><Overload retVal="" descr="create a symbolic link to a file"><Param name="OLDFILE" /><Param name="NEWFILE" /></Overload></KeyWord>
		<KeyWord name="syscall" func="yes"><Overload retVal="" descr="execute an arbitrary system call"><Param name="NUMBER" /><Param name="LIST" /></Overload></KeyWord>
		<KeyWord name="sysopen" func="yes"><Overload retVal="" descr="open a file, pipe, or descriptor"><Param name="FILEHANDLE" /><Param name="FILENAME" /><Param name="MODE" /></Overload><Overload retVal="" descr="open a file, pipe, or descriptor"><Param name="FILEHANDLE" /><Param name="FILENAME" /><Param name="MODE" /><Param name="PERMS" /></Overload></KeyWord>
		<KeyWord name="sysread" func="yes"><Overload retVal="" descr="fixed-length unbuffered input from a filehandle"><Param name="FILEHANDLE" /><Param name="SCALAR" /><Param name="LENGTH" /></Overload><Overload retVal="" descr="fixed-length unbuffered input from a filehandle"><Param name="FILEHANDLE" /><Param name="SCALAR" /><Param name="LENGTH" /><Param name="OFFSET" /></Overload></KeyWord>
		<KeyWord name="sysseek" func="yes"><Overload retVal="" descr="position I/O pointer on handle used with sysread and syswrite"><Param name="FILEHANDLE" /><Param name="POSITION" /><Param name="WHENCE" /></Overload></KeyWord>
		<KeyWord name="system" func="yes"><Overload retVal="" descr="run a separate program"><Param name="LIST" /></Overload><Overload retVal="" descr="run a separate program"><Param name="PROGRAM LIST" /></Overload></KeyWord>
		<KeyWord name="syswrite" func="yes"><Overload retVal="" descr="fixed-length unbuffered output to a filehandle"><Param name="FILEHANDLE" /><Param name="SCALAR" /></Overload><Overload retVal="" descr="fixed-length unbuffered output to a filehandle"><Param name="FILEHANDLE" /><Param name="SCALAR" /><Param name="LENGTH" /></Overload><Overload retVal="" descr="fixed-length unbuffered output to a filehandle"><Param name="FILEHANDLE" /><Param name="SCALAR" /><Param name="LENGTH" /><Param name="OFFSET" /></Overload></KeyWord>
		<KeyWord name="table" />
		<KeyWord name="tagConfigure" />
		<KeyWord name="td" />
		<KeyWord name="tell" func="yes"><Overload retVal="" descr="get current seekpointer on a filehandle"><Param name="FILEHANDLE" /></Overload><Overload retVal="" descr="get current seekpointer on a filehandle"><Param name="" /></Overload></KeyWord>
		<KeyWord name="telldir" func="yes"><Overload retVal="" descr="get current seekpointer on a directory handle"><Param name="DIRHANDLE" /></Overload></KeyWord>
		<KeyWord name="textarea" />
		<KeyWord name="textfield" />
		<KeyWord name="th" />
		<KeyWord name="threads" />
		<KeyWord name="tie" func="yes"><Overload retVal="" descr="bind a variable to an object class"><Param name="VARIABLE" /><Param name="CLASSNAME" /><Param name="LIST" /></Overload></KeyWord>
		<KeyWord name="tied" func="yes"><Overload retVal="" descr="get a reference to the object underlying a tied variable"><Param name="VARIABLE" /></Overload></KeyWord>
		<KeyWord name="time" func="yes"><Overload retVal="" descr="return number of seconds since 1970"><Param name="" /></Overload></KeyWord>
		<KeyWord name="times" func="yes"><Overload retVal="" descr="return elapsed time for self and child processes"><Param name="" /></Overload></KeyWord>
		<KeyWord name="title" />
		<KeyWord name="tmpFileName" />
		<KeyWord name="top" />
		<KeyWord name="tr" func="yes"><Overload retVal="" descr="transliterate a string"><Param name="" /></Overload></KeyWord>
		<KeyWord name="try" func="yes"><Overload retVal="" descr="exception handling: try a block"><Param name="BLOCK" /></Overload></KeyWord>
		<KeyWord name="truncate" func="yes"><Overload retVal="" descr="shorten a file"><Param name="EXPR" /><Param name="LENGTH" /></Overload><Overload retVal="" descr="shorten a file"><Param name="FILEHANDLE" /><Param name="LENGTH" /></Overload></KeyWord>
		<KeyWord name="uc" func="yes"><Overload retVal="" descr="return upper-case version of a string"><Param name="EXPR" /></Overload><Overload retVal="" descr="return upper-case version of a string"><Param name="" /></Overload></KeyWord>
		<KeyWord name="ucfirst" func="yes"><Overload retVal="" descr="return a string with just the next letter in upper case"><Param name="EXPR" /></Overload><Overload retVal="" descr="return a string with just the next letter in upper case"><Param name="" /></Overload></KeyWord>
		<KeyWord name="ul" />
		<KeyWord name="umask" func="yes"><Overload retVal="" descr="set file creation mode mask"><Param name="EXPR" /></Overload><Overload retVal="" descr="set file creation mode mask"><Param name="" /></Overload></KeyWord>
		<KeyWord name="undef" func="yes"><Overload retVal="" descr="remove a variable or function definition"><Param name="EXPR" /></Overload><Overload retVal="" descr="remove a variable or function definition"><Param name="" /></Overload></KeyWord>
		<KeyWord name="unescape" />
		<KeyWord name="unescapeHTML" />
		<KeyWord name="unless" />
		<KeyWord name="unlink" func="yes"><Overload retVal="" descr="remove one link to a file"><Param name="LIST" /></Overload><Overload retVal="" descr="remove one link to a file"><Param name="" /></Overload></KeyWord>
		<KeyWord name="unpack" func="yes"><Overload retVal="" descr="convert binary structure into normal perl variables"><Param name="TEMPLATE" /></Overload><Overload retVal="" descr="convert binary structure into normal perl variables"><Param name="TEMPLATE" /><Param name="EXPR" /></Overload></KeyWord>
		<KeyWord name="unshift" func="yes"><Overload retVal="" descr="prepend more elements to the beginning of a list"><Param name="ARRAY" /><Param name="LIST" /></Overload></KeyWord>
		<KeyWord name="untie" func="yes"><Overload retVal="" descr="break a tie binding to a variable"><Param name="VARIABLE" /></Overload></KeyWord>
		<KeyWord name="until" />
		<KeyWord name="update" />
		<KeyWord name="upload" />
		<KeyWord name="uploadInfo" />
		<KeyWord name="url" />
		<KeyWord name="url_param" />
		<KeyWord name="use" func="yes"><Overload retVal="" descr="load in a module at compile time and import its namespace"><Param name="Module" /></Overload><Overload retVal="" descr="load in a module at compile time and import its namespace"><Param name="Module LIST" /></Overload><Overload retVal="" descr="load in a module at compile time and import its namespace"><Param name="Module VERSION" /></Overload><Overload retVal="" descr="load in a module at compile time and import its namespace"><Param name="Module VERSION LIST" /></Overload><Overload retVal="" descr="load in a module at compile time and import its namespace"><Param name="VERSION" /></Overload></KeyWord>
		<KeyWord name="use_named_parameters" />
		<KeyWord name="user_agent" />
		<KeyWord name="user_name" />
		<KeyWord name="utf8" />
		<KeyWord name="utime" func="yes"><Overload retVal="" descr="set a file's last access and modify times"><Param name="LIST" /></Overload></KeyWord>
		<KeyWord name="values" func="yes"><Overload retVal="" descr="return a list of the values in a hash"><Param name="ARRAY" /></Overload><Overload retVal="" descr="return a list of the values in a hash"><Param name="HASH" /></Overload></KeyWord>
		<KeyWord name="variable" />
		<KeyWord name="vars" />
		<KeyWord name="vec" func="yes"><Overload retVal="" descr="test or set particular bits in a string"><Param name="EXPR" /><Param name="OFFSET" /><Param name="BITS" /></Overload></KeyWord>
		<KeyWord name="version" />
		<KeyWord name="virtual_host" />
		<KeyWord name="vmsish" />
		<KeyWord name="wait" func="yes"><Overload retVal="" descr="wait for any child process to die"><Param name="" /></Overload></KeyWord>
		<KeyWord name="waitpid" func="yes"><Overload retVal="" descr="wait for a particular child process to die"><Param name="PID" /><Param name="FLAGS" /></Overload></KeyWord>
		<KeyWord name="wantarray" func="yes"><Overload retVal="" descr="get void vs scalar vs list context of current subroutine call"><Param name="" /></Overload></KeyWord>
		<KeyWord name="warn" func="yes"><Overload retVal="" descr="print debugging info"><Param name="LIST" /></Overload></KeyWord>
		<KeyWord name="warnings" />
		<KeyWord name="when" />
		<KeyWord name="while" />
		<KeyWord name="width" />
		<KeyWord name="write" func="yes"><Overload retVal="" descr="print a picture record"><Param name="EXPR" /></Overload><Overload retVal="" descr="print a picture record"><Param name="FILEHANDLE" /></Overload><Overload retVal="" descr="print a picture record"><Param name="" /></Overload></KeyWord>
		<KeyWord name="x" />
		<KeyWord name="xor" />
		<KeyWord name="y" func="yes"><Overload retVal="" descr="transliterate a string (alternate form for tr//)"><Param name="" /></Overload></KeyWord>
	</AutoComplete>
</NotepadPlus>
