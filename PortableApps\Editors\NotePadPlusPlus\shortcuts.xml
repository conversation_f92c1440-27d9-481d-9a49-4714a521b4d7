<?xml version="1.0" encoding="UTF-8" ?>
<NotepadPlus>
    <InternalCommands />

    <!--
    You can organize you Macro menu & Run menu by adding the attribute 'FolderName="My sub-menu name"' to any Macro or Command node.
    This will place the corresponding node within a "My sub-menu name" sub-menu in the appropriate menu. Please consider the following examples:
    -->
    <Macros>
        <!--
        <Macro name="aa" Ctrl="no" Alt="no" Shift="no" Key="0">
            <Action type="1" message="2170" wParam="0" lParam="0" sParam="A" />
            <Action type="1" message="2170" wParam="0" lParam="0" sParam="A" />
        </Macro>
        <Macro name="az" Ctrl="no" Alt="no" Shift="no" Key="0" FolderName="words">
            <Action type="1" message="2170" wParam="0" lParam="0" sParam="a" />
            <Action type="1" message="2170" wParam="0" lParam="0" sParam="z" />
        </Macro>
            <Macro name="qw" Ctrl="no" Alt="no" Shift="no" Key="0" FolderName="words">
            <Action type="1" message="2170" wParam="0" lParam="0" sParam="q" />
            <Action type="1" message="2170" wParam="0" lParam="0" sParam="w" />
        </Macro>
        <Macro name="BB" Ctrl="no" Alt="no" Shift="no" Key="0">
            <Action type="1" message="2170" wParam="0" lParam="0" sParam="B" />
            <Action type="1" message="2170" wParam="0" lParam="0" sParam="B" />
        </Macro>
        <Macro name="Trim Trailing Space and Save" Ctrl="no" Alt="yes" Shift="yes" Key="83" FolderName="func">
            <Action type="2" message="0" wParam="42024" lParam="0" sParam="" />
            <Action type="2" message="0" wParam="41006" lParam="0" sParam="" />
        </Macro>
        -->
        <Macro name="Trim Trailing Space and Save" Ctrl="no" Alt="yes" Shift="yes" Key="83">
            <Action type="2" message="0" wParam="42024" lParam="0" sParam="" />
            <Action type="2" message="0" wParam="41006" lParam="0" sParam="" />
        </Macro>
    </Macros>
    <UserDefinedCommands>
        <!--
        <Command name="Get PHP help" Ctrl="no" Alt="yes" Shift="no" Key="112" FolderName="Help on Internet">https://www.php.net/$(CURRENT_WORD)</Command>
        <Command name="Wikipedia Search" Ctrl="no" Alt="yes" Shift="no" Key="114" FolderName="Help on Internet">https://en.wikipedia.org/wiki/Special:Search?search=$(CURRENT_WORD)</Command>
        <Command name="Open selected file path in new instance" Ctrl="no" Alt="yes" Shift="no" Key="117">$(NPP_FULL_FILE_PATH) $(CURRENT_WORD) -nosession -multiInst</Command>
        -->
        <Command name="Get PHP help" Ctrl="no" Alt="yes" Shift="no" Key="112">https://www.php.net/$(CURRENT_WORD)</Command>
        <Command name="Wikipedia Search" Ctrl="no" Alt="yes" Shift="no" Key="114">https://en.wikipedia.org/wiki/Special:Search?search=$(CURRENT_WORD)</Command>
        <Command name="Open selected file path in new instance" Ctrl="no" Alt="yes" Shift="no" Key="117">$(NPP_FULL_FILE_PATH) $(CURRENT_WORD) -nosession -multiInst</Command>
    </UserDefinedCommands>
    <PluginCommands />
    <ScintillaKeys />
</NotepadPlus>
