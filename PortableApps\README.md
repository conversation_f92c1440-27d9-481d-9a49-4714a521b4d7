# Windows Sandbox 便携应用集合

这个文件夹包含了一些精选的便携应用，可以在 Windows Sandbox 中快速部署使用。

## 文件夹结构

```
PortableApps/
├── Browsers/           # 浏览器
├── Editors/           # 编辑器
├── Development/       # 开发工具
├── System/           # 系统工具
├── Media/            # 媒体工具
├── Network/          # 网络工具
├── Security/         # 安全工具
└── Utilities/        # 实用工具
```

## 使用方法

1. 将需要的便携应用下载并解压到对应文件夹
2. 启动 Windows Sandbox 时，这些应用会自动复制到沙盒中
3. 在沙盒桌面上会创建相应的快捷方式

## 推荐的便携应用

### 浏览器 (Browsers/)
- **Firefox Portable** - Mozilla Firefox 便携版
- **Chrome Portable** - Google Chrome 便携版 (非官方)

### 编辑器 (Editors/)
- **Notepad++ Portable** - 强大的文本编辑器
- **VS Code Portable** - 轻量级代码编辑器

### 开发工具 (Development/)
- **Git Portable** - 版本控制工具
- **Python Portable** - Python 解释器
- **Node.js Portable** - JavaScript 运行时

### 系统工具 (System/)
- **Process Monitor** - 进程监控
- **Process Explorer** - 增强版任务管理器
- **Autoruns** - 启动项管理

### 媒体工具 (Media/)
- **VLC Portable** - 万能媒体播放器
- **IrfanView Portable** - 图片查看器

### 网络工具 (Network/)
- **Wireshark Portable** - 网络分析工具
- **Putty Portable** - SSH 客户端

### 安全工具 (Security/)
- **Malwarebytes Portable** - 恶意软件扫描
- **ClamWin Portable** - 杀毒软件

### 实用工具 (Utilities/)
- **7-Zip Portable** - 压缩工具
- **Everything Portable** - 文件搜索
- **TreeSize Portable** - 磁盘空间分析

## 下载链接

大部分便携应用可以从以下网站下载：
- [PortableApps.com](https://portableapps.com/)
- [Portable Freeware Collection](https://www.portablefreeware.com/)
- 各软件官方网站的便携版

## 注意事项

1. 确保下载的是便携版 (Portable) 或免安装版
2. 有些应用可能需要 .NET Framework 或 Visual C++ 运行库
3. 大型应用会增加沙盒启动时间
4. 建议定期更新便携应用到最新版本
