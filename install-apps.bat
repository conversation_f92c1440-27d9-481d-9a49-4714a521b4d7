@echo off
title Windows Sandbox - 应用安装脚本
echo ========================================
echo Windows Sandbox 应用自动安装
echo ========================================
echo.

REM 设置代理（如果需要）
echo [1/6] 配置网络代理...
for /f "tokens=3" %%i in ('route print ^| findstr "0.0.0.0"') do (
    if not "%%i"=="0.0.0.0" (
        set gateway=%%i
        goto :found_gateway
    )
)
:found_gateway
if not "%gateway%"=="" (
    netsh interface portproxy add v4tov4 listenaddress=127.0.0.1 listenport=7890 connectaddress=%gateway% connectport=7890
    reg add "HKCU\Software\Microsoft\Windows\CurrentVersion\Internet Settings" /v ProxyEnable /t REG_DWORD /d 1 /f 2>nul
    reg add "HKCU\Software\Microsoft\Windows\CurrentVersion\Internet Settings" /v ProxyServer /t REG_SZ /d "127.0.0.1:7890" /f 2>nul
    echo 代理配置完成: %gateway%:7890
)
echo.

REM 创建下载目录
mkdir C:\Downloads 2>nul
cd C:\Downloads

echo [2/6] 安装 Chocolatey 包管理器...
REM 安装 Chocolatey
powershell -Command "Set-ExecutionPolicy Bypass -Scope Process -Force; [System.Net.ServicePointManager]::SecurityProtocol = [System.Net.ServicePointManager]::SecurityProtocol -bor 3072; iex ((New-Object System.Net.WebClient).DownloadString('https://community.chocolatey.org/install.ps1'))"

REM 刷新环境变量
call refreshenv.cmd 2>nul
set PATH=%PATH%;C:\ProgramData\chocolatey\bin

echo.
echo [3/6] 安装常用应用...

REM 安装 Google Chrome
echo 正在安装 Google Chrome...
choco install googlechrome -y --no-progress

REM 安装 Firefox
echo 正在安装 Firefox...
choco install firefox -y --no-progress

REM 安装 7-Zip
echo 正在安装 7-Zip...
choco install 7zip -y --no-progress

REM 安装 Notepad++
echo 正在安装 Notepad++...
choco install notepadplusplus -y --no-progress

REM 安装 Git
echo 正在安装 Git...
choco install git -y --no-progress

echo.
echo [4/6] 安装开发工具...

REM 安装 Visual Studio Code
echo 正在安装 VS Code...
choco install vscode -y --no-progress

REM 安装 Python
echo 正在安装 Python...
choco install python -y --no-progress

REM 安装 Node.js
echo 正在安装 Node.js...
choco install nodejs -y --no-progress

echo.
echo [5/6] 配置桌面快捷方式...

REM 创建桌面快捷方式
powershell -Command "
$WshShell = New-Object -comObject WScript.Shell;
$Shortcut = $WshShell.CreateShortcut('C:\Users\<USER>\Desktop\Chrome.lnk');
$Shortcut.TargetPath = 'C:\Program Files\Google\Chrome\Application\chrome.exe';
$Shortcut.Save();

$Shortcut = $WshShell.CreateShortcut('C:\Users\<USER>\Desktop\Firefox.lnk');
$Shortcut.TargetPath = 'C:\Program Files\Mozilla Firefox\firefox.exe';
$Shortcut.Save();

$Shortcut = $WshShell.CreateShortcut('C:\Users\<USER>\Desktop\VS Code.lnk');
$Shortcut.TargetPath = 'C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\Code.exe';
$Shortcut.Save();
"

echo.
echo [6/6] 清理安装文件...
cd C:\
rmdir /s /q C:\Downloads 2>nul

echo.
echo ========================================
echo 安装完成！
echo ========================================
echo.
echo 已安装的应用:
echo - Google Chrome
echo - Mozilla Firefox  
echo - 7-Zip
echo - Notepad++
echo - Git
echo - Visual Studio Code
echo - Python
echo - Node.js
echo.
echo 桌面快捷方式已创建
echo 沙盒准备就绪！
echo.
pause
