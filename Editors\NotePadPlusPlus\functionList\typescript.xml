<?xml version="1.0" encoding="UTF-8" ?>
<!-- ==========================================================================\
|
|   To learn how to make your own language parser, please check the following
|   link:
|       https://npp-user-manual.org/docs/function-list/
|
\=========================================================================== -->
<NotepadPlus>
	<functionList>
		<!-- ================================================ [ TypeScript ] -->

		<parser
			displayName="TypeScript"
			id         ="typescript_function"
			commentExpr="(?s:/\*.*?\*/)|(?m-s://.*?$)"
		>
			<function
				mainExpr="((^|\s+|[;\}\.])([A-Za-z_$][\w$]*\.)*[A-Za-z_$][\w$]*\s*[=:]|^|[\s;\}]+)\s*function(\s+[A-Za-z_$][\w$]*)?\s*\([^\)\(]*\)[\n\s]*\{"
			>
				<functionName>
					<nameExpr expr="[A-Za-z_$][\w$]*\s*[=:]|[A-Za-z_$][\w$]*\s*\(" />
					<nameExpr expr="[A-Za-z_$][\w$]*" />
				</functionName>
				<className>
					<nameExpr expr="([A-Za-z_$][\w$]*\.)*[A-Za-z_$][\w$]*\." />
					<nameExpr expr="([A-Za-z_$][\w$]*\.)*[A-Za-z_$][\w$]*" />
				</className>
			</function>
		</parser>
	</functionList>
</NotepadPlus>