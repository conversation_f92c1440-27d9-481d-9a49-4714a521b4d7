<?xml version="1.0" encoding="UTF-8" ?>
<NotepadPlus>
	<AutoComplete>
        <KeyWord name="abstract" />
        <KeyWord name="any" />
        <KeyWord name="as" />
        <KeyWord name="async" />
        <KeyWord name="await" />
        <KeyWord name="bigint" />
        <KeyWord name="boolean" />
        <KeyWord name="break" />
        <KeyWord name="case" />
        <KeyWord name="catch" />
        <KeyWord name="class" />
        <KeyWord name="const" />
        <KeyWord name="continue" />
        <KeyWord name="debugger" />
        <KeyWord name="declare" />
        <KeyWord name="default" />
        <KeyWord name="delete" />
        <KeyWord name="do" />
        <KeyWord name="else" />
        <KeyWord name="enum" />
        <KeyWord name="export" />
        <KeyWord name="extends" />
        <KeyWord name="false" />
        <KeyWord name="finally" />
        <KeyWord name="for" />
        <KeyWord name="from" />
        <KeyWord name="function" />
        <KeyWord name="get" />
        <KeyWord name="if" />
        <KeyWord name="implements" />
        <KeyWord name="import" />
        <KeyWord name="in" />
        <KeyWord name="instanceof" />
        <KeyWord name="interface" />
        <KeyWord name="let" />
        <KeyWord name="module" />
        <KeyWord name="NaN" />
        <KeyWord name="new" />
        <KeyWord name="null" />
        <KeyWord name="number" />
        <KeyWord name="of" />
        <KeyWord name="package" />
        <KeyWord name="private" />
        <KeyWord name="protected" />
        <KeyWord name="prototype" />
        <KeyWord name="public" />
        <KeyWord name="require" />
        <KeyWord name="return" />
        <KeyWord name="set" />
        <KeyWord name="static" />
        <KeyWord name="string" />
        <KeyWord name="super" />
        <KeyWord name="switch" />
        <KeyWord name="symbol" />
        <KeyWord name="synchronized" />
        <KeyWord name="this" />
        <KeyWord name="throw" />
        <KeyWord name="true" />
        <KeyWord name="try" />
        <KeyWord name="type" />
        <KeyWord name="typeof" />
        <KeyWord name="undefined" />
        <KeyWord name="var" /> 
        <KeyWord name="void" />
        <KeyWord name="while" />
        <KeyWord name="with" />
        <KeyWord name="yield" />
	</AutoComplete>
</NotepadPlus>
