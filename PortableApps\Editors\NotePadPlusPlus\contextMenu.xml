<?xml version="1.0" encoding="UTF-8" ?>
<!--
By modifying this file, you can customize your context menu popuped as right clicking on the edit zone.
It may be more convinient to access to your frequent used commands via context menu than via the top menu.

Please check "How to Customize the Context Menu" on:
https://npp-user-manual.org/docs/config-files/#the-context-menu-contextmenu-xml
-->
<NotepadPlus>
    <ScintillaContextMenu>
		<!-- 
		Use MenuEntryName and MenuItemName to localize your commands to add. 
		The values should be in English but not in translated language.
		(You can set Notepad++ language back to English from Preferences dialog via menu "Settings->Preferences...")
		-->
        <Item MenuEntryName="Edit" MenuItemName="Cut"/>
        <Item MenuEntryName="Edit" MenuItemName="Copy"/>
        <Item MenuEntryName="Edit" MenuItemName="Paste"/>
        <Item MenuEntryName="Edit" MenuItemName="Delete"/>
        <Item MenuEntryName="Edit" MenuItemName="Select all"/>
        <Item MenuEntryName="Edit" MenuItemName="Begin/End Select"/>
        <Item MenuEntryName="Edit" MenuItemName="Begin/End Select in Column Mode"/>
		
		<!-- id="0" is the separator -->
        <Item id="0"/>
		
		<!-- You can use command id to add the commands you want. 
		Check english.xml to get commands id:
		https://github.com/notepad-plus-plus/notepad-plus-plus/blob/master/PowerEditor/installer/nativeLang/english.xml
		
		Use FolderName (optional) to create sub-menu. FolderName can be used in any type of item.
		FolderName value can be in any language.
		TranslateID is for translating FolderName's value. If you create your FolderName, don't add TranslateID.
		-->
        <Item FolderName="Style all occurrences of token" TranslateID="contextMenu-styleAlloccurrencesOfToken" id="43022"/>
        <Item FolderName="Style all occurrences of token" TranslateID="contextMenu-styleAlloccurrencesOfToken" id="43024"/>
        <Item FolderName="Style all occurrences of token" TranslateID="contextMenu-styleAlloccurrencesOfToken" id="43026"/>
        <Item FolderName="Style all occurrences of token" TranslateID="contextMenu-styleAlloccurrencesOfToken" id="43028"/>
        <Item FolderName="Style all occurrences of token" TranslateID="contextMenu-styleAlloccurrencesOfToken" id="43030"/>
		
        <Item FolderName="Style one token"  TranslateID="contextMenu-styleOneToken" id="43062"/>
        <Item FolderName="Style one token"  TranslateID="contextMenu-styleOneToken" id="43063"/>
        <Item FolderName="Style one token"  TranslateID="contextMenu-styleOneToken" id="43064"/>
        <Item FolderName="Style one token"  TranslateID="contextMenu-styleOneToken" id="43065"/>
        <Item FolderName="Style one token"  TranslateID="contextMenu-styleOneToken" id="43066"/>
		
        <Item FolderName="Clear style"  TranslateID="contextMenu-clearStyle" id="43023"/>
        <Item FolderName="Clear style"  TranslateID="contextMenu-clearStyle" id="43025"/>
        <Item FolderName="Clear style"  TranslateID="contextMenu-clearStyle" id="43027"/>
        <Item FolderName="Clear style"  TranslateID="contextMenu-clearStyle" id="43029"/>
        <Item FolderName="Clear style"  TranslateID="contextMenu-clearStyle" id="43031"/>
        <Item FolderName="Clear style"  TranslateID="contextMenu-clearStyle" id="43032"/>
        <Item id="0"/>
		
		<!--
		To add plugin commands, you have to use PluginEntryName and PluginCommandItemName to localize the plugin commands
		-->
		<Item FolderName="Plugin commands" TranslateID="contextMenu-PluginCommands" PluginEntryName="MIME Tools" PluginCommandItemName="Base64 Encode" />
        <Item FolderName="Plugin commands" TranslateID="contextMenu-PluginCommands" PluginEntryName="MIME Tools" PluginCommandItemName="Base64 Decode" />
		
		<!--
		Use ItemNameAs (optional) to rename the menu item name in the context menu 
		ItemNameAs can be used in any type of item. ItemNameAs value can be in any language.
		-->
        <Item FolderName="Plugin commands"  TranslateID="contextMenu-PluginCommands" PluginEntryName="NppExport" PluginCommandItemName="Copy all formats to clipboard" ItemNameAs="Copy Text with Syntax Highlighting" />
        <Item id="0"/>
        <Item MenuEntryName="Edit" MenuItemName="UPPERCASE"/>
        <Item MenuEntryName="Edit" MenuItemName="lowercase"/>
        <Item id="0"/>
        <Item MenuEntryName="Edit" MenuItemName="Open File"/>
        <Item MenuEntryName="Edit" MenuItemName="Search on Internet"/>
        <Item id="0"/>
        <Item MenuEntryName="Edit" MenuItemName="Toggle Single Line Comment"/>
        <Item MenuEntryName="Edit" MenuItemName="Block Comment"/>
        <Item MenuEntryName="Edit" MenuItemName="Block Uncomment"/>
        <Item id="0"/>
        <Item MenuEntryName="View" MenuItemName="Hide lines"/>
    </ScintillaContextMenu>
</NotepadPlus>
