开发工具便携版

已集成应用（通过简单下载器自动安装）：
1. VS Code Portable
   - 位置: VSCode/Code.exe
   - 功能: 轻量级代码编辑器
   - 启动: 双击 Code.exe 或使用桌面快捷方式

2. Python 3.11 Embedded
   - 位置: Python/python.exe
   - 功能: Python 编程环境
   - 包含: pip 包管理器（自动配置）
   - 启动: 双击 python.exe 或使用桌面快捷方式

3. Git Portable
   - 位置: Git/bin/git.exe
   - 功能: 版本控制系统
   - 包含: Git Bash, Git GUI
   - 启动: 双击 git-bash.exe 或使用桌面快捷方式

其他推荐应用（手动下载）：
4. Node.js Portable
   - 下载地址: https://nodejs.org/en/download/ (选择 zip 版本)
   - 解压到: NodeJS 文件夹
   - 功能: JavaScript 运行时环境

5. JDK Portable
   - 下载地址: https://adoptium.net/ (选择 zip 版本)
   - 解压到: JDK 文件夹
   - 功能: Java 开发工具包

6. Go Portable
   - 下载地址: https://golang.org/dl/ (选择 zip 版本)
   - 解压到: Go 文件夹
   - 功能: Go 语言开发环境

快速开始：
1. 运行 "简单下载器.bat" 自动下载核心开发工具
2. 运行 "配置开发环境.bat" 完成环境配置
3. 使用桌面快捷方式启动工具
4. 运行 "set_dev_env.bat" 配置环境变量

开发环境特性：
- 自动配置 Python pip
- 自动创建桌面快捷方式
- 自动设置环境变量脚本
- 完整的 Git 工具链
- VS Code 开箱即用
