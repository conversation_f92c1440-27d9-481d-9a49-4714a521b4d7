开发工具便携版

推荐应用：
1. Git Portable
   - 下载地址: https://git-scm.com/download/win (选择 Portable 版本)
   - 解压到: GitPortable 文件夹
   - 功能: 版本控制系统

2. Python Portable
   - 下载地址: https://portableapps.com/apps/development/python_portable
   - 解压到: PythonPortable 文件夹
   - 功能: Python 解释器和开发环境

3. Node.js Portable
   - 下载地址: https://nodejs.org/en/download/ (选择 zip 版本)
   - 解压到: NodeJSPortable 文件夹
   - 功能: JavaScript 运行时环境

4. JDK Portable
   - 下载地址: https://adoptium.net/ (选择 zip 版本)
   - 解压到: JDKPortable 文件夹
   - 功能: Java 开发工具包

5. Go Portable
   - 下载地址: https://golang.org/dl/ (选择 zip 版本)
   - 解压到: GoPortable 文件夹
   - 功能: Go 语言开发环境

使用说明：
- 解压后需要配置环境变量
- 沙盒启动脚本会自动配置 PATH
- 适合快速搭建开发环境
