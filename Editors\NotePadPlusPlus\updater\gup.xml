<?xml version="1.0" ?>
<!--
    Copyright 2007 <PERSON> <<EMAIL>>
	
    This file is part of WinGup.

    WinGup is free software: you can redistribute it and/or modify
    it under the terms of the GNU Lesser General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    WinGup is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU Lesser General Public License for more details.

    You should have received a copy of the GNU Lesser General Public License
    along with GUP.  If not, see <http://www.gnu.org/licenses/>.
-->
<GUPInput>
	<!-- optional.
	It's the current version of your program. WinGup will add "?version=versionNumber" at the end of InfoUrl.
	This parameter will be ignored if you pass directly your version number to <PERSON>Gup via command line "-v".
	-->
	<Version>4.6</Version>
	
	<!-- optional.
	It could be whatever you want.
	If this node is present and the VALUE is not empty,	<PERSON><PERSON><PERSON> will add "&param=VALUE" at the end of version param.
	This parameter will be ignored if you pass directly your param VALUE to <PERSON>G<PERSON> via command line "-p".
	-->
	<Param></Param>

	<!-- Mandatory.
	This is the url (your web application, both http and https are supported) from where your WinGup gets the update information.
	The tag "Version" value will be the parameter that your web application can use $_GET["version"] to get the current version of the program to be updated.
	With the current version value, your web application should return a set of information in xml form to tell WinGup update version and the location of update package to download.
	-->
	<!--InfoUrl>http://notepad-plus.sourceforge.net/commun/update/getDownLoadUrl.php</InfoUrl-->
	<InfoUrl>https://notepad-plus-plus.org/update/getDownloadUrl.php</InfoUrl>

	<!-- Optional. 
	The SoftwareName(plus its version) will be part of the User-Agent you want to use to download your binary:
	Notepad++/4.6 (WinGup/3.0)
	If this node is absent or empty, then only "WinGup/WINGUP_VERSION" will be used as User-Agent:
	WinGup/3.0
	-->
	<SoftwareName>Notepad++</SoftwareName>

	<!-- Optional.
	The SoftwareIcon will be used as icon for both taskbar and updater wherever applicable (e.g. whenever UI/messagebox is shown).
	"updater.ico" should be aside of GUP.exe.
	If this node is absent/empty, or "updater.ico" is absent, then windows default icon will be used.
	-->
	<SoftwareIcon>updater.ico</SoftwareIcon>

	<!-- Optional. 
	The window class name of program that you want to update.
	WinGup uses FindWindow routine to get the Handle of window, then send WM_CLOSE to the handle in order to quit the program.
	If there are several running instances, WinGup will kill one by one until the last instance.
	
	In win32 system, if a program is running, the binary file is locked. 
	Use this parameter to close the program to make sure the old binary files can be erased by new one.
	-->
	<ClassName2Close>Notepad++</ClassName2Close>
	
	<!-- Optional.
	This is the title to display on the message box title bar.
	
	If isModal value is "yes", then Asking Update dialog  will be modal. If the value is set "no" or isModal attribute is absent, this dialog will be non modal.
	If extraCmd is set (parameter is present and the value is not empty), the 3rd button "Never" will be appear (beside "Yes" and "No" button).
	extraCmd is the Windows Message (a numeric value) to notify your application that Cancel button is clicked by user.
	extraCmdButtonLabel is the 3rd buttons label. If its value is empty or this attribute is absent, then the default value "Never" will be used.
	extraCmd will be sent (with its wParam and lParam) to the handle of application indicated in ClassName2Close.
	extraCmd, ecWparam and ecLparam are optional.
	-->
	<!--MessageBoxTitle isModal="yes" extraCmd="36950" extraCmdButtonLabel="Maybe" ecWparam="0" ecLparam="0">Notepad++ update</MessageBoxTitle-->
	<!--MessageBoxTitle isModal="no" extraCmd="" ecWparam="" ecLparam="">Notepad++ update</MessageBoxTitle-->
	<MessageBoxTitle isModal="no">Notepad++ update</MessageBoxTitle>

	<!-- optional. YES by default.
	This parameter can hide all the network error message. 
	If "SilentMode" is set as "yes", then users won't be warned when there's no connection of internet or the url is not available. 
	-->
	<SilentMode>yes</SilentMode>
	
</GUPInput>
