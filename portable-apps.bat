@echo off
chcp 65001 >nul
title Windows Sandbox - 便携应用部署
echo ========================================
echo 便携应用快速部署
echo ========================================
echo.

REM 配置代理
echo [1/5] 配置网络代理...
for /f "tokens=3" %%i in ('route print ^| findstr "0.0.0.0"') do (
    if not "%%i"=="0.0.0.0" (
        set gateway=%%i
        goto :found_gateway
    )
)
:found_gateway
if not "%gateway%"=="" (
    netsh interface portproxy add v4tov4 listenaddress=127.0.0.1 listenport=7890 connectaddress=%gateway% connectport=7890
    reg add "HKCU\Software\Microsoft\Windows\CurrentVersion\Internet Settings" /v ProxyEnable /t REG_DWORD /d 1 /f 2>nul
    reg add "HKCU\Software\Microsoft\Windows\CurrentVersion\Internet Settings" /v ProxyServer /t REG_SZ /d "127.0.0.1:7890" /f 2>nul
    echo ✓ 代理配置完成: %gateway%:7890
) else (
    echo ⚠ 未找到网关，跳过代理配置
)

echo.
echo [2/5] 创建应用目录...
mkdir "C:\PortableApps" 2>nul
mkdir "C:\PortableApps\Browsers" 2>nul
mkdir "C:\PortableApps\Editors" 2>nul
mkdir "C:\PortableApps\Development" 2>nul
mkdir "C:\PortableApps\System" 2>nul
mkdir "C:\PortableApps\Media" 2>nul
mkdir "C:\PortableApps\Network" 2>nul
mkdir "C:\PortableApps\Security" 2>nul
mkdir "C:\PortableApps\Utilities" 2>nul
mkdir "C:\Users\<USER>\Desktop\Apps" 2>nul
echo ✓ 目录结构创建完成

echo.
echo [3/5] 部署便携应用...

REM 如果主机文件夹中有便携应用，复制到沙盒
if exist "C:\Users\<USER>\Desktop\HostFiles\PortableApps" (
    echo 正在复制便携应用...
    xcopy "C:\Users\<USER>\Desktop\HostFiles\PortableApps\*" "C:\PortableApps\" /E /I /Y /Q
    echo ✓ 便携应用复制完成
) else (
    echo ⚠ 未找到 PortableApps 文件夹，跳过复制
)

echo.
echo [4/5] 扫描并配置便携应用...

REM 扫描各类别文件夹中的应用
set app_count=0

REM 扫描浏览器
for /d %%d in ("C:\PortableApps\Browsers\*") do (
    if exist "%%d\*.exe" (
        echo ✓ 发现浏览器: %%~nd
        set /a app_count+=1
    )
)

REM 扫描编辑器
for /d %%d in ("C:\PortableApps\Editors\*") do (
    if exist "%%d\*.exe" (
        echo ✓ 发现编辑器: %%~nd
        set /a app_count+=1
    )
)

REM 扫描系统工具
for %%f in ("C:\PortableApps\System\*.exe") do (
    echo ✓ 发现系统工具: %%~nf
    set /a app_count+=1
)

REM 扫描实用工具
for /d %%d in ("C:\PortableApps\Utilities\*") do (
    if exist "%%d\*.exe" (
        echo ✓ 发现实用工具: %%~nd
        set /a app_count+=1
    )
)

for %%f in ("C:\PortableApps\Utilities\*.exe") do (
    echo ✓ 发现实用工具: %%~nf
    set /a app_count+=1
)

echo 共发现 %app_count% 个便携应用

echo.
echo [5/5] 创建桌面快捷方式...

REM 创建智能快捷方式
powershell -Command "
$WshShell = New-Object -comObject WScript.Shell
$desktop = 'C:\Users\<USER>\Desktop'
$shortcutCount = 0

# 常见应用快捷方式映射
$appMappings = @{
    'NotePadPlusPlus' = @{exe='notepad++.exe'; name='Notepad++'; icon='notepad++.exe'}
    'FirefoxPortable' = @{exe='FirefoxPortable.exe'; name='Firefox'; icon='App\Firefox\firefox.exe'}
    'GoogleChromePortable' = @{exe='GoogleChromePortable.exe'; name='Chrome'; icon='App\Chrome-bin\chrome.exe'}
    'VSCodePortable' = @{exe='Code.exe'; name='VS Code'; icon='Code.exe'}
    'Everything' = @{exe='Everything.exe'; name='Everything'; icon='Everything.exe'}
    '7ZipPortable' = @{exe='7ZipPortable.exe'; name='7-Zip'; icon='App\7-Zip\7zFM.exe'}
}

# 扫描并创建快捷方式
Get-ChildItem 'C:\PortableApps' -Recurse -Directory | ForEach-Object {
    $folderName = $_.Name
    if ($appMappings.ContainsKey($folderName)) {
        $mapping = $appMappings[$folderName]
        $exePath = Join-Path $_.FullName $mapping.exe
        if (Test-Path $exePath) {
            $shortcut = $WshShell.CreateShortcut((Join-Path $desktop ($mapping.name + '.lnk')))
            $shortcut.TargetPath = $exePath
            $shortcut.WorkingDirectory = $_.FullName
            $shortcut.Save()
            Write-Host ('✓ 创建快捷方式: ' + $mapping.name)
            $shortcutCount++
        }
    }
}

# 扫描单文件应用
Get-ChildItem 'C:\PortableApps' -Recurse -File -Filter '*.exe' | Where-Object {
    $_.Directory.Name -eq 'System' -or $_.Directory.Name -eq 'Utilities'
} | ForEach-Object {
    $shortcut = $WshShell.CreateShortcut((Join-Path $desktop ($_.BaseName + '.lnk')))
    $shortcut.TargetPath = $_.FullName
    $shortcut.WorkingDirectory = $_.Directory.FullName
    $shortcut.Save()
    Write-Host ('✓ 创建快捷方式: ' + $_.BaseName)
    $shortcutCount++
}

# 便携应用文件夹快捷方式
$shortcut = $WshShell.CreateShortcut((Join-Path $desktop '便携应用文件夹.lnk'))
$shortcut.TargetPath = 'C:\PortableApps'
$shortcut.Save()

Write-Host ('共创建 ' + $shortcutCount + ' 个应用快捷方式')
"

echo.
echo ========================================
echo 部署完成！
echo ========================================
echo.
echo 📁 便携应用位置: C:\PortableApps
echo 🖥️ 桌面快捷方式已创建
echo 🌐 网络代理已配置 (如果可用)
echo.
echo 💡 使用提示:
echo   • 在主机 HostFiles\PortableApps 文件夹中放置更多应用
echo   • 运行 '下载常用应用.bat' 自动下载推荐应用
echo   • 查看各文件夹中的说明文件了解更多信息
echo.
echo 🚀 沙盒环境准备就绪！
echo.
pause
