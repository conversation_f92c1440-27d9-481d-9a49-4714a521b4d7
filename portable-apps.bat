@echo off
title Windows Sandbox - 便携应用部署
echo ========================================
echo 便携应用快速部署
echo ========================================
echo.

REM 配置代理
echo [1/4] 配置网络代理...
for /f "tokens=3" %%i in ('route print ^| findstr "0.0.0.0"') do (
    if not "%%i"=="0.0.0.0" (
        set gateway=%%i
        goto :found_gateway
    )
)
:found_gateway
if not "%gateway%"=="" (
    netsh interface portproxy add v4tov4 listenaddress=127.0.0.1 listenport=7890 connectaddress=%gateway% connectport=7890
    reg add "HKCU\Software\Microsoft\Windows\CurrentVersion\Internet Settings" /v ProxyEnable /t REG_DWORD /d 1 /f 2>nul
    reg add "HKCU\Software\Microsoft\Windows\CurrentVersion\Internet Settings" /v ProxyServer /t REG_SZ /d "127.0.0.1:7890" /f 2>nul
    echo 代理配置完成
)

echo.
echo [2/4] 创建应用目录...
mkdir "C:\PortableApps" 2>nul
mkdir "C:\Users\<USER>\Desktop\Apps" 2>nul

echo.
echo [3/4] 部署便携应用...

REM 如果主机文件夹中有便携应用，复制到沙盒
if exist "C:\Users\<USER>\Desktop\HostFiles\PortableApps" (
    echo 复制便携应用...
    xcopy "C:\Users\<USER>\Desktop\HostFiles\PortableApps\*" "C:\PortableApps\" /E /I /Y
)

REM 下载一些轻量级便携应用
echo 下载便携版应用...

REM 下载 Notepad++ 便携版
powershell -Command "
try {
    $url = 'https://github.com/notepad-plus-plus/notepad-plus-plus/releases/download/v8.5.8/npp.8.5.8.portable.x64.zip'
    $output = 'C:\PortableApps\notepadpp.zip'
    Invoke-WebRequest -Uri $url -OutFile $output -UseBasicParsing
    Expand-Archive -Path $output -DestinationPath 'C:\PortableApps\NotePadPP' -Force
    Remove-Item $output
    Write-Host 'Notepad++ 下载完成'
} catch {
    Write-Host 'Notepad++ 下载失败: ' + $_.Exception.Message
}
"

REM 下载 7-Zip 便携版
powershell -Command "
try {
    $url = 'https://www.7-zip.org/a/7z2301-x64.exe'
    $output = 'C:\PortableApps\7zip.exe'
    Invoke-WebRequest -Uri $url -OutFile $output -UseBasicParsing
    Write-Host '7-Zip 下载完成'
} catch {
    Write-Host '7-Zip 下载失败: ' + $_.Exception.Message
}
"

echo.
echo [4/4] 创建桌面快捷方式...

REM 创建快捷方式
powershell -Command "
$WshShell = New-Object -comObject WScript.Shell

# Notepad++ 快捷方式
if (Test-Path 'C:\PortableApps\NotePadPP\notepad++.exe') {
    $Shortcut = $WshShell.CreateShortcut('C:\Users\<USER>\Desktop\Notepad++.lnk')
    $Shortcut.TargetPath = 'C:\PortableApps\NotePadPP\notepad++.exe'
    $Shortcut.Save()
}

# 应用文件夹快捷方式
$Shortcut = $WshShell.CreateShortcut('C:\Users\<USER>\Desktop\便携应用.lnk')
$Shortcut.TargetPath = 'C:\PortableApps'
$Shortcut.Save()
"

echo.
echo ========================================
echo 部署完成！
echo ========================================
echo.
echo 便携应用位置: C:\PortableApps
echo 桌面快捷方式已创建
echo.
echo 提示: 您可以在主机的 HostFiles\PortableApps 文件夹中
echo 放置更多便携应用，下次启动时会自动复制
echo.
pause
