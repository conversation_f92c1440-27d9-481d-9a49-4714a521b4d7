@echo off
chcp 65001 >nul
title 便携应用下载器
echo ========================================
echo 便携应用自动下载器
echo ========================================
echo.

echo 正在创建下载目录...
mkdir Downloads 2>nul
cd Downloads

echo.
echo 开始下载常用便携应用...
echo.

REM 下载 Notepad++
echo [1/8] 下载 Notepad++ Portable...
powershell -Command "try { Invoke-WebRequest -Uri 'https://github.com/notepad-plus-plus/notepad-plus-plus/releases/download/v8.5.8/npp.8.5.8.portable.x64.zip' -OutFile 'notepadpp.zip' -UseBasicParsing; Write-Host '✓ Notepad++ 下载完成' } catch { Write-Host '✗ Notepad++ 下载失败' }"

REM 下载 7-Zip
echo [2/8] 下载 7-Zip...
powershell -Command "try { Invoke-WebRequest -Uri 'https://www.7-zip.org/a/7z2301-x64.exe' -OutFile '7zip.exe' -UseBasicParsing; Write-Host '✓ 7-Zip 下载完成' } catch { Write-Host '✗ 7-Zip 下载失败' }"

REM 下载 Process Monitor
echo [3/8] 下载 Process Monitor...
powershell -Command "try { Invoke-WebRequest -Uri 'https://download.sysinternals.com/files/ProcessMonitor.zip' -OutFile 'procmon.zip' -UseBasicParsing; Write-Host '✓ Process Monitor 下载完成' } catch { Write-Host '✗ Process Monitor 下载失败' }"

REM 下载 Process Explorer
echo [4/8] 下载 Process Explorer...
powershell -Command "try { Invoke-WebRequest -Uri 'https://download.sysinternals.com/files/ProcessExplorer.zip' -OutFile 'procexp.zip' -UseBasicParsing; Write-Host '✓ Process Explorer 下载完成' } catch { Write-Host '✗ Process Explorer 下载失败' }"

REM 下载 Everything
echo [5/8] 下载 Everything...
powershell -Command "try { Invoke-WebRequest -Uri 'https://www.voidtools.com/Everything-1.4.1.1024.x64.zip' -OutFile 'everything.zip' -UseBasicParsing; Write-Host '✓ Everything 下载完成' } catch { Write-Host '✗ Everything 下载失败' }"

REM 下载 Git Portable
echo [6/8] 下载 Git Portable...
powershell -Command "try { Invoke-WebRequest -Uri 'https://github.com/git-for-windows/git/releases/download/v2.42.0.windows.2/PortableGit-********-64-bit.7z.exe' -OutFile 'git-portable.exe' -UseBasicParsing; Write-Host '✓ Git Portable 下载完成' } catch { Write-Host '✗ Git Portable 下载失败' }"

REM 下载 Firefox Portable
echo [7/8] 下载 Firefox Portable...
powershell -Command "try { Invoke-WebRequest -Uri 'https://download.mozilla.org/?product=firefox-latest&os=win64&lang=zh-CN' -OutFile 'firefox-setup.exe' -UseBasicParsing; Write-Host '✓ Firefox 安装包下载完成' } catch { Write-Host '✗ Firefox 下载失败' }"

REM 下载 VLC Portable
echo [8/8] 下载 VLC Portable...
powershell -Command "try { Invoke-WebRequest -Uri 'https://get.videolan.org/vlc/3.0.18/win64/vlc-3.0.18-win64.zip' -OutFile 'vlc.zip' -UseBasicParsing; Write-Host '✓ VLC 下载完成' } catch { Write-Host '✗ VLC 下载失败' }"

echo.
echo ========================================
echo 解压应用到对应文件夹...
echo ========================================

REM 解压 Notepad++
if exist "notepadpp.zip" (
    echo 解压 Notepad++...
    powershell -Command "Expand-Archive -Path 'notepadpp.zip' -DestinationPath '../Editors/NotePadPlusPlus' -Force"
)

REM 解压 Process Monitor
if exist "procmon.zip" (
    echo 解压 Process Monitor...
    powershell -Command "Expand-Archive -Path 'procmon.zip' -DestinationPath '../System' -Force"
)

REM 解压 Process Explorer
if exist "procexp.zip" (
    echo 解压 Process Explorer...
    powershell -Command "Expand-Archive -Path 'procexp.zip' -DestinationPath '../System' -Force"
)

REM 解压 Everything
if exist "everything.zip" (
    echo 解压 Everything...
    powershell -Command "Expand-Archive -Path 'everything.zip' -DestinationPath '../Utilities/Everything' -Force"
)

REM 解压 VLC
if exist "vlc.zip" (
    echo 解压 VLC...
    powershell -Command "Expand-Archive -Path 'vlc.zip' -DestinationPath '../Media' -Force"
)

REM 移动单文件应用
if exist "7zip.exe" (
    echo 移动 7-Zip...
    move "7zip.exe" "..\Utilities\7zip.exe"
)

echo.
echo ========================================
echo 清理下载文件...
echo ========================================
cd ..
rmdir /s /q Downloads 2>nul

echo.
echo ========================================
echo 下载完成！
echo ========================================
echo.
echo 已下载的应用：
echo ✓ Notepad++ (Editors/NotePadPlusPlus/)
echo ✓ 7-Zip (Utilities/7zip.exe)
echo ✓ Process Monitor (System/ProcMon.exe)
echo ✓ Process Explorer (System/procexp.exe)
echo ✓ Everything (Utilities/Everything/)
echo ✓ VLC Media Player (Media/vlc-*)
echo.
echo 注意：某些应用可能需要手动安装或配置
echo 建议检查各文件夹中的说明文件
echo.
pause
