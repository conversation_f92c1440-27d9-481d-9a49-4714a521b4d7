@echo off
chcp 65001 >nul
title Windows Sandbox 启动器
color 0A

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    Windows Sandbox 启动器                    ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.
echo 请选择启动模式：
echo.
echo [1] 🚀 增强模式 - 自动安装常用应用 (推荐)
echo [2] 📦 便携模式 - 使用预置便携应用
echo [3] 🎯 自定义模式 - 选择性安装应用
echo [4] 🔧 基础模式 - 仅配置代理
echo [5] 📁 管理便携应用文件夹
echo [0] ❌ 退出
echo.
set /p choice=请输入选择 (0-5): 

if "%choice%"=="1" goto :enhanced
if "%choice%"=="2" goto :portable
if "%choice%"=="3" goto :custom
if "%choice%"=="4" goto :basic
if "%choice%"=="5" goto :manage
if "%choice%"=="0" goto :exit
goto :invalid

:enhanced
echo.
echo 🚀 启动增强模式沙盒...
echo 将自动安装: Chrome, Firefox, VS Code, Git, Python, Node.js 等
echo.
if exist "enhanced-sandbox.wsb" (
    start "" "enhanced-sandbox.wsb"
    echo ✓ 沙盒已启动，请等待应用自动安装完成
) else (
    echo ❌ 未找到 enhanced-sandbox.wsb 配置文件
)
goto :end

:portable
echo.
echo 📦 启动便携应用模式...
echo 将使用 PortableApps 文件夹中的应用
echo.
if exist "PortableApps" (
    if exist "proxy-sandbox.wsb" (
        start "" "proxy-sandbox.wsb"
        echo ✓ 沙盒已启动，便携应用将自动部署
    ) else (
        echo ❌ 未找到 proxy-sandbox.wsb 配置文件
    )
) else (
    echo ❌ 未找到 PortableApps 文件夹
    echo 请先运行选项 5 创建便携应用文件夹
)
goto :end

:custom
echo.
echo 🎯 启动自定义模式...
echo 将提供应用选择菜单
echo.
if exist "proxy-sandbox.wsb" (
    start "" "proxy-sandbox.wsb"
    echo ✓ 沙盒已启动，请在沙盒中运行 custom-apps.bat
) else (
    echo ❌ 未找到 proxy-sandbox.wsb 配置文件
)
goto :end

:basic
echo.
echo 🔧 启动基础模式...
echo 仅配置网络代理
echo.
if exist "proxy-sandbox.wsb" (
    start "" "proxy-sandbox.wsb"
    echo ✓ 基础沙盒已启动
) else (
    echo ❌ 未找到 proxy-sandbox.wsb 配置文件
)
goto :end

:manage
echo.
echo 📁 便携应用管理
echo.
echo [1] 📂 打开 PortableApps 文件夹
echo [2] 📥 运行自动下载脚本
echo [3] 📋 查看应用列表
echo [4] 🔙 返回主菜单
echo.
set /p manage_choice=请选择 (1-4): 

if "%manage_choice%"=="1" (
    if exist "PortableApps" (
        start "" "PortableApps"
    ) else (
        echo ❌ PortableApps 文件夹不存在
    )
    goto :manage
)
if "%manage_choice%"=="2" (
    if exist "PortableApps\下载常用应用.bat" (
        cd PortableApps
        call "下载常用应用.bat"
        cd ..
    ) else (
        echo ❌ 下载脚本不存在
    )
    goto :manage
)
if "%manage_choice%"=="3" (
    if exist "PortableApps\README.md" (
        start "" "PortableApps\README.md"
    ) else (
        echo ❌ README 文件不存在
    )
    goto :manage
)
if "%manage_choice%"=="4" goto :start
goto :manage

:invalid
echo.
echo ❌ 无效选择，请重新输入
timeout /t 2 >nul
goto :start

:exit
echo.
echo 👋 再见！
timeout /t 1 >nul
exit /b 0

:end
echo.
echo 💡 提示：
echo   • 沙盒启动后，应用安装可能需要几分钟时间
echo   • 确保主机代理软件正在运行 (端口 7890)
echo   • 如有问题，请检查防火墙设置
echo.
pause
exit /b 0

:start
cls
goto :0
