<?xml version="1.0" encoding="UTF-8" ?>
<NotepadPlus>
	<!-- note: this list was created using functions and reserved words known to GnuCOBOL (COBOL85,2002,2014 + extensions from IBM/MF/RM/ACUCOBOL) -->
	<AutoComplete language="COBOL">
		<Environment ignoreCase="yes" startFunc="(" stopFunc=")" paramSeparator="," additionalWordChar="-" />
		<KeyWord name="3-D" />
		<KeyWord name="ABS" func="yes">
			<Overload retVal="Integer/Numeric" descr="absolute value of &lt;number&gt;">
				<Param name="number" />
			</Overload>
		</KeyWord>
		<KeyWord name="ACCEPT" />
		<KeyWord name="ACCESS" />
		<KeyWord name="ACOS" func="yes">
			<Overload retVal="Numeric" descr="trigonometric arc-cosine, or inverse cosine, of &lt;cosine&gt;">
				<Param name="cosine" />
			</Overload>
		</KeyWord>
		<KeyWord name="ACTIVE-CLASS" />
		<KeyWord name="ADD" />
		<KeyWord name="ADDRESS" />
		<KeyWord name="ADVANCING" />
		<KeyWord name="AFTER" />
		<KeyWord name="ALIGNED" />
		<KeyWord name="ALL" />
		<KeyWord name="ALLOCATE" />
		<KeyWord name="ALPHABET" />
		<KeyWord name="ALPHABETIC" />
		<KeyWord name="ALPHABETIC-LOWER" />
		<KeyWord name="ALPHABETIC-UPPER" />
		<KeyWord name="ALPHANUMERIC" />
		<KeyWord name="ALPHANUMERIC-EDITED" />
		<KeyWord name="ALSO" />
		<KeyWord name="ALTER" />
		<KeyWord name="ALTERNATE" />
		<KeyWord name="AND" />
		<KeyWord name="ANNUITY" func="yes">
			<Overload retVal="Numeric" descr="numeric value approximating the ratio of an annuity paid at the specified &lt;interest-rate&gt; for each of the specified &lt;number-of-periods&gt;">
				<Param name="interest-rate" />
				<Param name="number-of-periods" />
			</Overload>
		</KeyWord>
		<KeyWord name="ANY" />
		<KeyWord name="ANYCASE" />
		<KeyWord name="APPLY" />
		<KeyWord name="ARE" />
		<KeyWord name="AREA" />
		<KeyWord name="AREAS" />
		<KeyWord name="ARITHMETIC" />
		<KeyWord name="AS" />
		<KeyWord name="ASCENDING" />
		<KeyWord name="ASIN" func="yes">
			<Overload retVal="Numeric" descr="trigonometric arc-sine, or inverse sine, of &lt;sine&gt;">
				<Param name="sine" />
			</Overload>
		</KeyWord>
		<KeyWord name="ASSIGN" />
		<KeyWord name="AT" />
		<KeyWord name="ATAN" func="yes">
			<Overload retVal="Numeric" descr="trigonometric arc-tangent, or inverse tangent, of &lt;tangent&gt;">
				<Param name="tangent" />
			</Overload>
		</KeyWord>
		<KeyWord name="ATTRIBUTE" />
		<KeyWord name="AUTHOR" />
		<KeyWord name="AUTO" />
		<KeyWord name="AUTOMATIC" />
		<KeyWord name="AWAY-FROM-ZERO" />
		<KeyWord name="B-AND" />
		<KeyWord name="B-NOT" />
		<KeyWord name="B-OR" />
		<KeyWord name="B-XOR" />
		<KeyWord name="BACKGROUND" />
		<KeyWord name="BACKGROUND-COLOR" />
		<KeyWord name="BACKGROUND-HIGH" />
		<KeyWord name="BACKGROUND-LOW" />
		<KeyWord name="BAR" />
		<KeyWord name="BASED" />
		<KeyWord name="BEFORE" />
		<KeyWord name="BELL" />
		<KeyWord name="BINARY" />
		<KeyWord name="BINARY-C-LONG" />
		<KeyWord name="BINARY-CHAR" />
		<KeyWord name="BINARY-DOUBLE" />
		<KeyWord name="BINARY-ENCODING" />
		<KeyWord name="BINARY-LONG" />
		<KeyWord name="BINARY-SEQUENTIAL" />
		<KeyWord name="BINARY-SHORT" />
		<KeyWord name="BIT" />
		<KeyWord name="BLANK" />
		<KeyWord name="BLINK" />
		<KeyWord name="BLOB" />
		<KeyWord name="BLOCK" />
		<KeyWord name="BOLD" />
		<KeyWord name="BOOLEAN" />
		<KeyWord name="BOOLEAN-OF-INTEGER" func="yes">
			<Overload retVal="Boolean" descr="boolean item of usage bit representing the binary value of &lt;integer&gt; with the given &lt;length&gt;">
				<Param name="integer" />
				<Param name="length" />
			</Overload>
		</KeyWord>
		<KeyWord name="BOTTOM" />
		<KeyWord name="BOX" />
		<KeyWord name="BUSY" />
		<KeyWord name="BY" />
		<KeyWord name="BYTE-LENGTH" func="yes">
			<Overload retVal="Integer" descr="length (in bytes) of &lt;item&gt;">
				<Param name="item" />
				<!-- not known to be supported by any compiler <Param name="[PHYSICAL]" /> -->
			</Overload>
		</KeyWord>
		<KeyWord name="C01" />
		<KeyWord name="C02" />
		<KeyWord name="C03" />
		<KeyWord name="C04" />
		<KeyWord name="C05" />
		<KeyWord name="C06" />
		<KeyWord name="C07" />
		<KeyWord name="C08" />
		<KeyWord name="C09" />
		<KeyWord name="C10" />
		<KeyWord name="C11" />
		<KeyWord name="C12" />
		<KeyWord name="CALL" />
		<KeyWord name="CALL-CONVENTION" />
		<KeyWord name="CANCEL" />
		<KeyWord name="CAPACITY" />
		<KeyWord name="CBL" />
		<KeyWord name="CD" />
		<KeyWord name="CENTER" />
		<KeyWord name="CF" />
		<KeyWord name="CH" />
		<KeyWord name="CHAINING" />
		<KeyWord name="CHAR" func="yes">
			<Overload retVal="Alphanumeric" descr="character in the ordinal position specified by &lt;integer&gt; from the current alphanumeric COLLATING SEQUENCE">
				<Param name="integer" />
			</Overload>
		</KeyWord>
		<KeyWord name="CHAR-NATIONAL" func="yes">
			<Overload retVal="National" descr="character in the ordinal position specified by &lt;integer&gt; from the current national COLLATING SEQUENCE">
				<Param name="integer" />
			</Overload>
		</KeyWord>
		<KeyWord name="CHARACTER" />
		<KeyWord name="CHARACTERS" />
		<KeyWord name="CLASS" />
		<KeyWord name="CLASS-ID" />
		<KeyWord name="CLASSIFICATION" />
		<KeyWord name="CLOB" />
		<KeyWord name="CLOCK-UNITS" />
		<KeyWord name="CLOSE" />
		<KeyWord name="COB-CRT-STATUS" />
		<KeyWord name="COBOL" />
		<KeyWord name="CODE-SET" />
		<KeyWord name="COL" />
		<KeyWord name="COLLATING" />
		<KeyWord name="COLS" />
		<KeyWord name="COLUMN" />
		<KeyWord name="COLUMNS" />
		<KeyWord name="COMBINED-DATETIME" func="yes">
			<Overload retVal="Numeric" descr="combines &lt;integer-date&gt; and &lt;standard-numeric-time&gt; into a single numeric item from which both date and time components can be derived">
				<Param name="integer-date" />
				<Param name="standard-numeric-time" />
			</Overload>
		</KeyWord>
		<KeyWord name="COMBO-BOX" />
		<KeyWord name="COMMA" />
		<KeyWord name="COMMIT" />
		<KeyWord name="COMMON" />
		<KeyWord name="COMMUNICATION" />
		<KeyWord name="COMP" />
		<KeyWord name="COMP-1" />
		<KeyWord name="COMP-3" />
		<KeyWord name="COMP-4" />
		<KeyWord name="COMP-5" />
		<KeyWord name="COMP-6" />
		<KeyWord name="COMP-X" />
		<KeyWord name="COMPUTATIONAL" />
		<KeyWord name="COMPUTATIONAL-1" />
		<KeyWord name="COMPUTATIONAL-3" />
		<KeyWord name="COMPUTATIONAL-4" />
		<KeyWord name="COMPUTATIONAL-5" />
		<KeyWord name="COMPUTATIONAL-6" />
		<KeyWord name="COMPUTATIONAL-X" />
		<KeyWord name="COMPUTE" />
		<KeyWord name="CONCATENATE" func="yes">
			<Overload retVal="Alphanumeric" descr="concatenates the &lt;string&gt;, ... items together into a single string result">
				<Param name="string" />
				<Param name="..." />
			</Overload>
		</KeyWord>
		<KeyWord name="CONDITION" />
		<KeyWord name="CONFIGURATION" />
		<KeyWord name="CONSOLE" />
		<KeyWord name="CONSTANT" />
		<KeyWord name="CONTAINS" />
		<KeyWord name="CONTENT" />
		<KeyWord name="CONTINUE" />
		<KeyWord name="CONTROL" />
		<KeyWord name="CONTROLS" />
		<KeyWord name="CONVERTING" />
		<KeyWord name="COPY" />
		<KeyWord name="CORR" />
		<KeyWord name="CORRESPONDING" />
		<KeyWord name="COS" func="yes">
			<Overload retVal="Numeric" descr="trigonometric cosine of &lt;angle&gt;">
				<Param name="angle" />
			</Overload>
		</KeyWord>
		<KeyWord name="COUNT" />
		<KeyWord name="CRT" />
		<KeyWord name="CSP" />
		<KeyWord name="CURRENCY" />
		<KeyWord name="CURRENCY-SYMBOL" func="yes">
			<Overload retVal="Alphanumeric" descr="currency symbol character currently in effect for the locale under which the program is running"/>
		</KeyWord>
		<KeyWord name="CURRENT-DATE" func="yes">
			<Overload retVal="Alphanumeric" descr="current date and time as 21-character value"/>
		</KeyWord>
		<KeyWord name="CURSOR" />
		<KeyWord name="CYCLE" />
		<KeyWord name="DATA" />
		<KeyWord name="DATA-POINTER" />
		<KeyWord name="DATE" />
		<KeyWord name="DATE-COMPILED" />
		<KeyWord name="DATE-MODIFIED" />
		<KeyWord name="DATE-OF-INTEGER" func="yes">
			<Overload retVal="Integer" descr="converts &lt;integer-date&gt; (in the Gregorian calendar) to standard date form (YYYYMMDD)">
				<Param name="integer-date" />
			</Overload>
		</KeyWord>
		<KeyWord name="DATE-TO-YYYYMMDD" func="yes">
			<Overload retVal="Integer" descr="convert the six-digit Gregorian date &lt;yymmdd&gt; to a seven-digit numeric Julian format with optional &lt;yy-cutoff&gt; to delineate centuries">
				<Param name="yymmdd" />
				<Param name="[yy-cutoff, default: 50]" />
				<Param name="[yy-execution-time, default: now]" />
			</Overload>
		</KeyWord>
		<KeyWord name="DATE-WRITTEN" />
		<KeyWord name="DAY" />
		<KeyWord name="DAY-OF-INTEGER" func="yes">
			<Overload retVal="Integer" descr="convert &lt;integer&gt; to calendar date in yyyyddd (i.e. Julian) format">
				<Param name="integer" />
			</Overload>
		</KeyWord>
		<KeyWord name="DAY-OF-WEEK" />
		<KeyWord name="DAY-TO-YYYYDDD" func="yes">
			<Overload retVal="Integer" descr="convert the five-digit Julian date &lt;yyddd&gt; to a seven-digit numeric Julian format with optional &lt;yy-cutoff&gt; to delineate centuries">
				<Param name="yyddd" />
				<Param name="[yy-cutoff, default: 50]" />
				<Param name="[yy-execution-time, default: now]" />
			</Overload>
		</KeyWord>
		<KeyWord name="DBCLOB" />
		<KeyWord name="DBCS" />
		<KeyWord name="DE" />
		<KeyWord name="DEBUG-CONTENTS" />
		<KeyWord name="DEBUG-ITEM" />
		<KeyWord name="DEBUG-LINE" />
		<KeyWord name="DEBUG-NAME" />
		<KeyWord name="DEBUG-SUB-1" />
		<KeyWord name="DEBUG-SUB-2" />
		<KeyWord name="DEBUG-SUB-3" />
		<KeyWord name="DEBUGGING" />
		<KeyWord name="DECIMAL-ENCODING" />
		<KeyWord name="DECIMAL-POINT" />
		<KeyWord name="DECLARATIVES" />
		<KeyWord name="DEFAULT" />
		<KeyWord name="DELETE" />
		<KeyWord name="DELIMITED" />
		<KeyWord name="DELIMITER" />
		<KeyWord name="DEPENDING" />
		<KeyWord name="DESCENDING" />
		<KeyWord name="DESTINATION" />
		<KeyWord name="DESTROY" />
		<KeyWord name="DETAIL" />
		<KeyWord name="DIALECT-ALL" />
		<KeyWord name="DISABLE" />
		<KeyWord name="DISPLAY" />
		<KeyWord name="DISPLAY-OF" func="yes">
			<Overload retVal="Alphanumeric" descr="convert &lt;national-string&gt; to the alphanumeric coded character set representation">
				<Param name="national-string" />
				<Param name="[replacement-char]" />
			</Overload>
		</KeyWord>
		<KeyWord name="DIVIDE" />
		<KeyWord name="DIVISION" />
		<KeyWord name="DOWN" />
		<KeyWord name="DRAW" />
		<KeyWord name="DROP" />
		<KeyWord name="DUPLICATES" />
		<KeyWord name="DYNAMIC" />
		<KeyWord name="E" func="yes">
			<Overload retVal="Numeric" descr="approximation of e, the base of natural logarithms"/>
		</KeyWord>
		<KeyWord name="EC" />
		<KeyWord name="EGI" />
		<KeyWord name="ELSE" />
		<KeyWord name="EMI" />
		<KeyWord name="ENABLE" />
		<KeyWord name="END" />
		<KeyWord name="END-ACCEPT" />
		<KeyWord name="END-ADD" />
		<KeyWord name="END-CALL" />
		<KeyWord name="END-COMPUTE" />
		<KeyWord name="END-DELETE" />
		<KeyWord name="END-DISPLAY" />
		<KeyWord name="END-DIVIDE" />
		<KeyWord name="END-EVALUATE" />
		<KeyWord name="END-EXEC" />
		<KeyWord name="END-IF" />
		<KeyWord name="END-MULTIPLY" />
		<KeyWord name="END-OF-PAGE" />
		<KeyWord name="END-PERFORM" />
		<KeyWord name="END-READ" />
		<KeyWord name="END-RECEIVE" />
		<KeyWord name="END-RETURN" />
		<KeyWord name="END-REWRITE" />
		<KeyWord name="END-SEARCH" />
		<KeyWord name="END-START" />
		<KeyWord name="END-STRING" />
		<KeyWord name="END-SUBTRACT" />
		<KeyWord name="END-UNSTRING" />
		<KeyWord name="END-WRITE" />
		<KeyWord name="ENTER" />
		<KeyWord name="ENTRY" />
		<KeyWord name="ENTRY-CONVENTION" />
		<KeyWord name="ENVIRONMENT" />
		<KeyWord name="EO" />
		<KeyWord name="EOL" />
		<KeyWord name="EOP" />
		<KeyWord name="EOS" />
		<KeyWord name="EQUAL" />
		<KeyWord name="EQUALS" />
		<KeyWord name="ERASE" />
		<KeyWord name="ERROR" />
		<KeyWord name="ESI" />
		<KeyWord name="EVALUATE" />
		<KeyWord name="EVERY" />
		<KeyWord name="EXCEPTION" />
		<KeyWord name="EXCEPTION-FILE" func="yes">
			<Overload retVal="Alphanumeric" descr="character string that is the I-O status value and file-name of the file connector, if any, associated with the last exception status"/>
		</KeyWord>
		<KeyWord name="EXCEPTION-FILE-N" func="yes">
			<Overload retVal="National" descr="character string that is the I-O status value and file-name of the file connector, if any, associated with the last exception status"/>
		</KeyWord>
		<KeyWord name="EXCEPTION-LOCATION" func="yes">
			<Overload retVal="Alphanumeric" descr="character string with the location of the statement associated with the last exception status"/>
		</KeyWord>
		<KeyWord name="EXCEPTION-LOCATION-N" func="yes">
			<Overload retVal="National" descr="character string with the location of the statement associated with the last exception status"/>
		</KeyWord>
		<KeyWord name="EXCEPTION-OBJECT" />
		<KeyWord name="EXCEPTION-STATEMENT" func="yes">
			<Overload retVal="Alphanumeric" descr="name of the statement that caused the associated exception condition"/>
		</KeyWord>
		<KeyWord name="EXCEPTION-STATUS" func="yes">
			<Overload retVal="Alphanumeric" descr="exception-name associated with the last exception status"/>
		</KeyWord>
		<KeyWord name="EXCLUSIVE" />
		<KeyWord name="EXEC" />
		<KeyWord name="EXIT" />
		<KeyWord name="EXP" func="yes">
			<Overload retVal="Numeric" descr="approximation of e raised to the power of &lt;number&gt;">
				<Param name="number" />
			</Overload>
		</KeyWord>
		<KeyWord name="EXP10" func="yes">
			<Overload retVal="Numeric" descr="approximation of 10 raised to the power of &lt;number&gt;">
				<Param name="number" />
			</Overload>
		</KeyWord>
		<KeyWord name="EXPANDS" />
		<KeyWord name="EXTEND" />
		<KeyWord name="EXTERNAL" />
		<!-- <KeyWord name="F" /> -->
		<KeyWord name="FACTORIAL" func="yes">
			<Overload retVal="Integer" descr="factorial of &lt;number&gt;">
				<Param name="number" />
			</Overload>
		</KeyWord>
		<KeyWord name="FACTORY" />
		<KeyWord name="FALSE" />
		<KeyWord name="FARTHEST-FROM-ZERO" />
		<KeyWord name="FD" />
		<KeyWord name="FILE" />
		<KeyWord name="FILE-CONTROL" />
		<KeyWord name="FILLER" />
		<KeyWord name="FINAL" />
		<KeyWord name="FIRST" />
		<KeyWord name="FLOAT-BINARY" />
		<KeyWord name="FLOAT-BINARY-128" />
		<KeyWord name="FLOAT-BINARY-32" />
		<KeyWord name="FLOAT-BINARY-64" />
		<KeyWord name="FLOAT-DECIMAL" />
		<KeyWord name="FLOAT-DECIMAL-16" />
		<KeyWord name="FLOAT-DECIMAL-34" />
		<KeyWord name="FLOAT-EXTENDED" />
		<KeyWord name="FLOAT-INFINITY" />
		<KeyWord name="FLOAT-LONG" />
		<KeyWord name="FLOAT-NOT-A-NUMBER" />
		<KeyWord name="FLOAT-NOT-A-NUMBER-QUIET" />
		<KeyWord name="FLOAT-NOT-A-NUMBER-SIGNALING" />
		<KeyWord name="FLOAT-SHORT" />
		<KeyWord name="FOOTING" />
		<KeyWord name="FOR" />
		<KeyWord name="FOREGROUND-COLOR" />
		<KeyWord name="FOREVER" />
		<KeyWord name="FORMAT" />
		<KeyWord name="FORMATTED-CURRENT-DATE" func="yes">
			<Overload retVal="Alphanumeric/National" descr="current date and time provided by the system at run-time, formatted according to &lt;date-and-time-format&gt;">
				<Param name="date-and-time-format" />
			</Overload>
		</KeyWord>
		<KeyWord name="FORMATTED-DATE" func="yes">
			<Overload retVal="Alphanumeric/National" descr="&lt;integer-date&gt;, formatted according to &lt;date-format&gt;">
				<Param name="date-format" />
				<Param name="integer-date" />
			</Overload>
		</KeyWord>
		<KeyWord name="FORMATTED-DATETIME" func="yes">
			<Overload retVal="Alphanumeric/National" descr="&lt;integer-date&gt; and &lt;standard-numeric-time&gt;, formatted according to &lt;date-and-time-format&gt; with optional &lt;offset&gt; from UTC in minutes">
				<Param name="date-and-time-format" />
				<Param name="integer-date" />
				<Param name="standard-numeric-time" />
				<Param name="[offset | SYSTEM-OFFSET]" />
			</Overload>
		</KeyWord>
		<KeyWord name="FORMATTED-TIME" func="yes">
			<Overload retVal="Alphanumeric/National" descr="&lt;standard-numeric-time&gt;, formatted according to &lt;time-format&gt; with optional &lt;offset&gt; from UTC in minutes">
				<Param name="time-format" />
				<Param name="standard-numeric-time" />
				<Param name="[offset | SYSTEM-OFFSET]" />
			</Overload>
		</KeyWord>
		<KeyWord name="FORMFEED" />
		<KeyWord name="FRACTION-PART" func="yes">
			<Overload retVal="Numeric" descr="portion of &lt;number&gt; that occurs to the right of the decimal point">
				<Param name="number" />
			</Overload>
		</KeyWord>
		<KeyWord name="FREE" />
		<KeyWord name="FROM" />
		<KeyWord name="FULL" />
		<KeyWord name="FUNCTION" />
		<KeyWord name="FUNCTION-ID" />
		<KeyWord name="FUNCTION-POINTER" />
		<KeyWord name="GENERATE" />
		<KeyWord name="GET" />
		<KeyWord name="GIVING" />
		<KeyWord name="GLOBAL" />
		<KeyWord name="GO" />
		<KeyWord name="GOBACK" />
		<KeyWord name="GREATER" />
		<KeyWord name="GRID" />
		<KeyWord name="GRIP" />
		<KeyWord name="GROUP" />
		<KeyWord name="GROUP-USAGE" />
		<KeyWord name="HEADING" />
		<KeyWord name="HIGH" />
		<KeyWord name="HIGH-ORDER-LEFT" />
		<KeyWord name="HIGH-ORDER-RIGHT" />
		<KeyWord name="HIGH-VALUE" />
		<KeyWord name="HIGH-VALUES" />
		<KeyWord name="HIGHEST-ALGEBRAIC" func="yes">
			<Overload retVal="Integer/Numeric" descr="highest value that could possibly be stored in the specified &lt;numeric-identifier&gt;">
				<Param name="numeric-identifier" />
			</Overload>
		</KeyWord>
		<KeyWord name="HIGHLIGHT" />
		<KeyWord name="HOT-TRACK" />
		<KeyWord name="HSCROLL" />
		<KeyWord name="HSCROLL-POS" />
		<KeyWord name="I-O" />
		<KeyWord name="I-O-CONTROL" />
		<KeyWord name="ID" />
		<KeyWord name="IDENTIFICATION" />
		<KeyWord name="IF" />
		<KeyWord name="IGNORING" />
		<KeyWord name="IMP" />
		<KeyWord name="IMPLEMENTS" />
		<KeyWord name="IN" />
		<KeyWord name="IN-ARITHMETIC-RANGE" />
		<KeyWord name="INDEX" />
		<KeyWord name="INDEXED" />
		<KeyWord name="INDICATE" />
		<KeyWord name="INHERITS" />
		<KeyWord name="INITIAL" />
		<KeyWord name="INITIALIZE" />
		<KeyWord name="INITIALIZED" />
		<KeyWord name="INITIATE" />
		<KeyWord name="INPUT" />
		<KeyWord name="INPUT-OUTPUT" />
		<KeyWord name="INQUIRE" />
		<KeyWord name="INSPECT" />
		<KeyWord name="INSTALLATION" />
		<KeyWord name="INTEGER" func="yes">
			<Overload retVal="Integer" descr="greatest integer value that is less than or equal to &lt;number&gt;">
				<Param name="number" />
			</Overload>
		</KeyWord>
		<KeyWord name="INTEGER-OF-BOOLEAN" func="yes">
			<Overload retVal="Integer" descr="numeric value of &lt;boolean-item&gt;">
				<Param name="boolean-item" />
			</Overload>
		</KeyWord>
		<KeyWord name="INTEGER-OF-DATE" func="yes">
			<Overload retVal="Integer" descr="converts &lt;yyyymmdd&gt; to an internal integer-date">
				<Param name="yyyymmdd" />
			</Overload>
		</KeyWord>
		<KeyWord name="INTEGER-OF-DAY" func="yes">
			<Overload retVal="Integer" descr="converts &lt;yyyyddd&gt; to an internal integer-date">
				<Param name="yyyyddd" />
			</Overload>
		</KeyWord>
		<KeyWord name="INTEGER-OF-FORMATTED-DATE" func="yes">
			<Overload retVal="Integer" descr="converts &lt;date&gt; in specified &lt;format&gt; to an internal integer-date">
				<Param name="format" />
				<Param name="date" />
			</Overload>
		</KeyWord>
		<KeyWord name="INTEGER-PART" func="yes">
			<Overload retVal="Integer" descr="portion of &lt;number&gt; that occurs to the left of the decimal point">
				<Param name="number" />
			</Overload>
		</KeyWord>
		<KeyWord name="INTERFACE" />
		<KeyWord name="INTERFACE-ID" />
		<KeyWord name="INTERMEDIATE" />
		<KeyWord name="INTO" />
		<KeyWord name="INTRINSIC" />
		<KeyWord name="INVALID" />
		<KeyWord name="INVOKE" />
		<KeyWord name="IS" />
		<KeyWord name="JNIENVPTR" />
		<KeyWord name="JSON-CODE" />
		<KeyWord name="JUST" />
		<KeyWord name="JUSTIFIED" />
		<KeyWord name="KANJI" />
		<KeyWord name="KEY" />
		<KeyWord name="LABEL" />
		<KeyWord name="LAST" />
		<KeyWord name="LC_ALL" />
		<KeyWord name="LC_COLLATE" />
		<KeyWord name="LC_CTYPE" />
		<KeyWord name="LC_MESSAGES" />
		<KeyWord name="LC_MONETARY" />
		<KeyWord name="LC_NUMERIC" />
		<KeyWord name="LC_TIME" />
		<KeyWord name="LEADING" />
		<KeyWord name="LEFT" />
		<KeyWord name="LENGTH" func="yes">
			<Overload retVal="Integer" descr="returns the length (in character positions) of the specified &lt;item&gt;">
				<Param name="item" />
				<!-- not known to be supported by any compiler <Param name="[PHYSICAL]" /> -->
			</Overload>
		</KeyWord>
		<KeyWord name="LENGTH-AN" func="yes">
			<Overload retVal="Integer" descr="returns the length (in bytes) of the specified &lt;item&gt;">
				<Param name="item" />
			</Overload>
		</KeyWord>
		<KeyWord name="LESS" />
		<KeyWord name="LIMIT" />
		<KeyWord name="LIMITS" />
		<KeyWord name="LINAGE" />
		<KeyWord name="LINAGE-COUNTER" />
		<KeyWord name="LINE" />
		<KeyWord name="LINE-COUNTER" />
		<KeyWord name="LINE-SEQUENTIAL" />
		<KeyWord name="LINES" />
		<KeyWord name="LINKAGE" />
		<KeyWord name="LOCAL-STORAGE" />
		<KeyWord name="LOCALE" />
		<KeyWord name="LOCALE-COMPARE" func="yes">
			<Overload retVal="Alphanumeric" descr="character '=' or '&lt;' or '&gt;' indicating the result of comparing &lt;argument-1&gt; and &lt;argument-2&gt; using a culturally-preferred ordering defined by a &lt;locale&gt;">
				<Param name="argument-1" />
				<Param name="argument-2" />
				<Param name="[locale, default: current]" />
			</Overload>
		</KeyWord>
		<KeyWord name="LOCALE-DATE" func="yes">
			<Overload retVal="Alphanumeric" descr="format &lt;yyyymmdd&gt; according to &lt;locale&gt;">
				<Param name="yyyymmdd" />
				<Param name="[locale, default: current]" />
			</Overload>
		</KeyWord>
		<KeyWord name="LOCALE-TIME" func="yes">
			<Overload retVal="Alphanumeric" descr="format &lt;time&gt; (HHMM or HHMMSS) according to &lt;locale&gt;">
				<Param name="time" />
				<Param name="[locale, default: current]" />
			</Overload>
		</KeyWord>
		<KeyWord name="LOCALE-TIME-FROM-SECONDS" func="yes">
			<Overload retVal="Alphanumeric" descr="format &lt;integer-time&gt; (internal-format) according to &lt;locale&gt;">
				<Param name="integer-time" />
				<Param name="[locale, default: current]" />
			</Overload>
		</KeyWord>
		<KeyWord name="LOCK" />
		<KeyWord name="LOG" func="yes">
			<Overload retVal="Numeric" descr="base e logarithm of &lt;number&gt;">
				<Param name="number" />
			</Overload>
		</KeyWord>
		<KeyWord name="LOG10" func="yes">
			<Overload retVal="Numeric" descr="base 10 logarithm of &lt;number&gt;">
				<Param name="number" />
			</Overload>
		</KeyWord>
		<KeyWord name="LOW" />
		<KeyWord name="LOW-VALUE" />
		<KeyWord name="LOW-VALUES" />
		<KeyWord name="LOWER-CASE" func="yes">
			<Overload retVal="Alphanumeric/National" descr="character string that contains &lt;string&gt; with any uppercase letters replaced by their corresponding lowercase letters">
				<Param name="string" />
			</Overload>
		</KeyWord>
		<KeyWord name="LOWEST-ALGEBRAIC" func="yes">
			<Overload retVal="Integer/Numeric" descr="lowest value that could possibly be stored in the specified &lt;numeric-identifier&gt;">
				<Param name="numeric-identifier" />
			</Overload>
		</KeyWord>
		<KeyWord name="LOWLIGHT" />
		<KeyWord name="MANUAL" />
		<KeyWord name="MAX" func="yes">
			<Overload retVal="Alphanumeric/Index/Integer/National/Numeric" descr="maximum value from the specified list of numbers">
				<Param name="number" />
				<Param name="..." />
			</Overload>
		</KeyWord>
		<KeyWord name="MEAN" func="yes">
			<Overload retVal="Numeric" descr="statistical mean value of the specified list of numbers">
				<Param name="number" />
				<Param name="..." />
			</Overload>
		</KeyWord>
		<KeyWord name="MEDIAN" func="yes">
			<Overload retVal="Numeric" descr="statistical median value of the specified list of numbers">
				<Param name="number" />
				<Param name="..." />
			</Overload>
		</KeyWord>
		<KeyWord name="MEMORY" />
		<KeyWord name="MERGE" />
		<KeyWord name="MESSAGE" />
		<KeyWord name="METHOD" />
		<KeyWord name="METHOD-ID" />
		<KeyWord name="MIDRANGE" func="yes">
			<Overload retVal="Numeric" descr="arithmetic mean (average) of the values of the minimum and maximum numbers from the supplied list">
				<Param name="number" />
				<Param name="..." />
			</Overload>
		</KeyWord>
		<KeyWord name="MIN" func="yes">
			<Overload retVal="Alphanumeric/Index/Integer/National/Numeric" descr="minimum value from the specified list of numbers">
				<Param name="number" />
				<Param name="..." />
			</Overload>
		</KeyWord>
		<KeyWord name="MINUS" />
		<KeyWord name="MOD" func="yes">
			<Overload retVal="Integer" descr="remainder from the division of &lt;value&gt; by &lt;modulus&gt;">
				<Param name="value" />
				<Param name="modulus" />
			</Overload>
		</KeyWord>
		<KeyWord name="MODE" />
		<KeyWord name="MODIFY" />
		<KeyWord name="MODULE-CALLER-ID" func="yes">
			<Overload retVal="Alphanumeric" descr="module that called the program (empty for main programs)"/>
		</KeyWord>
		<KeyWord name="MODULE-DATE" func="yes">
			<Overload retVal="Numeric" descr="date in yyyymmdd format at compile-time"/>
		</KeyWord>
		<KeyWord name="MODULE-FORMATTED-DATE" func="yes">
			<Overload retVal="Alphanumeric" descr="formatted date at compile-time"/>
		</KeyWord>
		<KeyWord name="MODULE-ID" func="yes">
			<Overload retVal="Alphanumeric" descr="value of PROGRAM-ID/FUNCTION-ID"/>
		</KeyWord>
		<KeyWord name="MODULE-PATH" func="yes">
			<Overload retVal="Alphanumeric" descr="full executable path from run-time"/>
		</KeyWord>
		<KeyWord name="MODULE-SOURCE" func="yes">
			<Overload retVal="Alphanumeric" descr="full source path used at compile-time"/>
		</KeyWord>
		<KeyWord name="MODULE-TIME" func="yes">
			<Overload retVal="Numeric" descr="time in hhmmss format at compile-time"/>
		</KeyWord>
		<KeyWord name="MODULES" />
		<KeyWord name="MONETARY-DECIMAL-POINT" func="yes">
			<Overload retVal="Alphanumeric" descr="character used to separate the integer portion from the fractional part of a monetary currency value according to the current locale"/>
		</KeyWord>
		<KeyWord name="MONETARY-THOUSANDS-SEPARATOR" func="yes">
			<Overload retVal="Alphanumeric" descr="character used to separate the thousands digit groupings in a monetary currency value according to the current locale"/>"/>
		</KeyWord>
		<KeyWord name="MOVE" />
		<KeyWord name="MULTIPLE" />
		<KeyWord name="MULTIPLY" />
		<KeyWord name="NATIONAL" />
		<KeyWord name="NATIONAL-EDITED" />
		<KeyWord name="NATIONAL-OF" func="yes">
			<Overload retVal="National" descr="convert &lt;alphanumeric-string&gt; to the national coded character set representation">
				<Param name="alphanumeric-string" />
				<Param name="[replacement-char]" />
			</Overload>
		</KeyWord>
		<KeyWord name="NATIVE" />
		<KeyWord name="NEAREST-AWAY-FROM-ZERO" />
		<KeyWord name="NEAREST-EVEN-INTERMEDIATE" />
		<KeyWord name="NEAREST-TO-ZERO" />
		<KeyWord name="NEAREST-TOWARD-ZERO" />
		<KeyWord name="NEGATIVE" />
		<KeyWord name="NEGATIVE-INFINITY" />
		<KeyWord name="NESTED" />
		<KeyWord name="NEXT" />
		<KeyWord name="NO" />
		<KeyWord name="NONE" />
		<KeyWord name="NORMAL" />
		<KeyWord name="NOT" />
		<KeyWord name="NULL" />
		<KeyWord name="NUM-ROWS" />
		<KeyWord name="NUMBER" />
		<KeyWord name="NUMBER-OF-CALL-PARAMETERS" />
		<KeyWord name="NUMBERS" />
		<KeyWord name="NUMERIC" />
		<KeyWord name="NUMERIC-DECIMAL-POINT" func="yes">
			<Overload retVal="Alphanumeric" descr="character used to separate the integer portion from the fractional part of a non-integer numeric value according to the current locale"/>
		</KeyWord>
		<KeyWord name="NUMERIC-EDITED" />
		<KeyWord name="NUMERIC-THOUSANDS-SEPARATOR" func="yes">
			<Overload retVal="Alphanumeric" descr="character used to separate the thousands digit groupings in a numeric value according to the current locale"/>
		</KeyWord>
		<KeyWord name="NUMVAL" func="yes">
			<Overload retVal="Numeric" descr="corresponding numeric value for &lt;string&gt;">
				<Param name="string" />
			</Overload>
		</KeyWord>
		<KeyWord name="NUMVAL-C" func="yes">
			<Overload retVal="Numeric" descr="corresponding numeric value for &lt;string&gt;, case-sensitive if ANYCASE not given">
				<Param name="string (may contain currency-symbol)" />
				<Param name="[currency-symbol if not default | LOCALE [locale-name] ]" />
				<Param name="[ANYCASE]" />
			</Overload>
		</KeyWord>
		<KeyWord name="NUMVAL-F" func="yes">
			<Overload retVal="Numeric" descr="corresponding numeric value for &lt;floating-point-string&gt;">
				<Param name="floating-point-string" />
			</Overload>
		</KeyWord>
		<KeyWord name="O-FILL" />
		<KeyWord name="OBJECT" />
		<KeyWord name="OBJECT-COMPUTER" />
		<KeyWord name="OBJECT-REFERENCE" />
		<KeyWord name="OCCURS" />
		<KeyWord name="OF" />
		<KeyWord name="OFF" />
		<KeyWord name="OMITTED" />
		<KeyWord name="ON" />
		<KeyWord name="ONLY" />
		<KeyWord name="OOSTACKPTR" />
		<KeyWord name="OPEN" />
		<KeyWord name="OPTIONAL" />
		<KeyWord name="OPTIONS" />
		<KeyWord name="OR" />
		<KeyWord name="ORD" func="yes">
			<Overload retVal="Integer" descr="ordinal position in the program character set corresponding to &lt;char&gt;">
				<Param name="char" />
			</Overload>
		</KeyWord>
		<KeyWord name="ORD-MAX" func="yes">
			<Overload retVal="Integer" descr="max. ordinal position in the program character set corresponding to list of &lt;char&gt;">
				<Param name="char" />
				<Param name="..." />
			</Overload>
		</KeyWord>
		<KeyWord name="ORD-MIN" func="yes">
			<Overload retVal="Integer" descr="min. ordinal position in the program character set corresponding to list of &lt;char&gt;">
				<Param name="char" />
				<Param name="..." />
			</Overload>
		</KeyWord>
		<KeyWord name="ORDER" />
		<KeyWord name="ORGANIZATION" />
		<KeyWord name="OTHER" />
		<KeyWord name="OUTPUT" />
		<KeyWord name="OVERFLOW" />
		<KeyWord name="OVERRIDE" />
		<KeyWord name="PACKED-DECIMAL" />
		<KeyWord name="PADDING" />
		<KeyWord name="PAGE" />
		<KeyWord name="PAGE-COUNTER" />
		<KeyWord name="PARAGRAPH" />
		<KeyWord name="PERFORM" />
		<KeyWord name="PF" />
		<KeyWord name="PH" />
		<KeyWord name="PHYSICAL" />
		<KeyWord name="PI" func="yes">
			<Overload retVal="Numeric" descr="approximation of p, the ratio of the circumference of a circle to its diameter"/>
		</KeyWord>
		<KeyWord name="PIC" />
		<KeyWord name="PICTURE" />
		<KeyWord name="PLUS" />
		<KeyWord name="POINTER" />
		<KeyWord name="POS" />
		<KeyWord name="POSITION" />
		<KeyWord name="POSITIVE" />
		<KeyWord name="POSITIVE-INFINITY" />
		<KeyWord name="PREFIXED" />
		<KeyWord name="PRESENT" />
		<KeyWord name="PRESENT-VALUE" func="yes">
			<Overload retVal="Numeric" descr="approximation of the present value of a series of future period-end &lt;amount&gt; arguments at a &lt;discount-rate&gt;">
				<Param name="discount-rate" />
				<Param name="amount" />
				<Param name="..." />
			</Overload>
		</KeyWord>
		<KeyWord name="PREVIOUS" />
		<KeyWord name="PRINT" />
		<KeyWord name="PRINTER" />
		<KeyWord name="PRINTER-1" />
		<KeyWord name="PRINTING" />
		<KeyWord name="PROCEDURE" />
		<KeyWord name="PROCEDURES" />
		<KeyWord name="PROCEED" />
		<KeyWord name="PROGRAM" />
		<KeyWord name="PROGRAM-ID" />
		<KeyWord name="PROGRAM-POINTER" />
		<KeyWord name="PROHIBITED" />
		<KeyWord name="PROMPT" />
		<KeyWord name="PROPERTY" />
		<KeyWord name="PROTOTYPE" />
		<KeyWord name="PUBLIC" />
		<KeyWord name="PURGE" />
		<KeyWord name="QUEUE" />
		<KeyWord name="QUOTE" />
		<KeyWord name="QUOTES" />
		<KeyWord name="RAISE" />
		<KeyWord name="RAISING" />
		<KeyWord name="RANDOM" func="yes">
			<Overload retVal="Numeric" descr="pseudo-random number 0&gt;=n&lt;1 from a rectangular distribution with optional &lt;seed&gt;">
				<Param name="[seed]" />
			</Overload>
		</KeyWord>
		<KeyWord name="RANGE" func="yes">
			<Overload retVal="Integer/Numeric" descr="value of the maximum &lt;argument&gt; minus the value of the minimum &lt;argument&gt;">
				<Param name="argument" />
				<Param name="..." />
			</Overload>
		</KeyWord>
		<KeyWord name="RD" />
		<KeyWord name="READ" />
		<KeyWord name="READY" />
		<KeyWord name="RECEIVE" />
		<KeyWord name="RECORD" />
		<KeyWord name="RECORDS" />
		<KeyWord name="RECURSIVE" />
		<KeyWord name="REDEFINES" />
		<KeyWord name="REEL" />
		<KeyWord name="REFERENCE" />
		<KeyWord name="REFERENCES" />
		<KeyWord name="RELATION" />
		<KeyWord name="RELATIVE" />
		<KeyWord name="RELEASE" />
		<KeyWord name="REM" func="yes">
			<Overload retVal="Numeric" descr="remainder of &lt;number&gt; divided by &lt;divisor&gt;">
				<Param name="number" />
				<Param name="divisor" />
			</Overload>
		</KeyWord>
		<KeyWord name="REMAINDER" />
		<KeyWord name="REMARKS" />
		<KeyWord name="REMOVAL" />
		<KeyWord name="RENAMES" />
		<KeyWord name="REPLACE" />
		<KeyWord name="REPLACING" />
		<KeyWord name="REPORT" />
		<KeyWord name="REPORTING" />
		<KeyWord name="REPORTS" />
		<KeyWord name="REPOSITORY" />
		<KeyWord name="REQUIRED" />
		<KeyWord name="RERUN" />
		<KeyWord name="RESERVE" />
		<KeyWord name="RESET" />
		<KeyWord name="RESUME" />
		<KeyWord name="RETRY" />
		<KeyWord name="RETURN" />
		<KeyWord name="RETURN-CODE" />
		<KeyWord name="RETURN-CODE-UNSIGNED" />
		<KeyWord name="RETURNING" />
		<KeyWord name="REVERSE" func="yes">
			<Overload retVal="Alphanumeric/National" descr="reverse representation with same length of &lt;string&gt;">
				<Param name="string" />
			</Overload>
		</KeyWord>
		<KeyWord name="REVERSE-VIDEO" />
		<KeyWord name="REVERSED" />
		<KeyWord name="REWIND" />
		<KeyWord name="REWRITE" />
		<KeyWord name="RF" />
		<KeyWord name="RH" />
		<KeyWord name="RIGHT" />
		<KeyWord name="ROLLBACK" />
		<KeyWord name="ROUNDED" />
		<KeyWord name="ROUNDING" />
		<KeyWord name="ROWID" />
		<KeyWord name="RUN" />
		<!-- <KeyWord name="S" /> -->
		<KeyWord name="SAME" />
		<KeyWord name="SCREEN" />
		<KeyWord name="SCROLL" />
		<KeyWord name="SCROLL-BAR" />
		<KeyWord name="SD" />
		<KeyWord name="SEARCH" />
		<KeyWord name="SECONDS" />
		<KeyWord name="SECONDS-FROM-FORMATTED-TIME" func="yes">
			<Overload retVal="Numeric" descr="decode &lt;time&gt; according to &lt;format&gt; (a time format or a combined date and time format)">
				<Param name="format" />
				<Param name="time" />
			</Overload>
		</KeyWord>
		<KeyWord name="SECONDS-PAST-MIDNIGHT" func="yes">
			<Overload retVal="Numeric" descr="current time of day expressed as the total number of elapsed seconds since midnight"/>
		</KeyWord>
		<KeyWord name="SECTION" />
		<KeyWord name="SECURE" />
		<KeyWord name="SECURITY" />
		<KeyWord name="SEGMENT" />
		<KeyWord name="SEGMENT-LIMIT" />
		<KeyWord name="SELECT" />
		<KeyWord name="SELF" />
		<KeyWord name="SEND" />
		<KeyWord name="SENTENCE" />
		<KeyWord name="SEPARATE" />
		<KeyWord name="SEQUENCE" />
		<KeyWord name="SEQUENTIAL" />
		<KeyWord name="SET" />
		<KeyWord name="SHADOW" />
		<KeyWord name="SHARING" />
		<KeyWord name="SHIFT-IN" />
		<KeyWord name="SHIFT-OUT" />
		<KeyWord name="SHORT" />
		<KeyWord name="SIGN" func="yes">
			<Overload retVal="Integer" descr="sign representation of &lt;number&gt; as -1, 0, 1">
				<Param name="number" />
			</Overload>
		</KeyWord>
		<KeyWord name="SIGNED" />
		<KeyWord name="SIN" func="yes">
			<Overload retVal="Numeric" descr="trigonometric sine of the specified &lt;angle&gt;">
				<Param name="angle" />
			</Overload>
		</KeyWord>
		<KeyWord name="SIZE" />
		<KeyWord name="SKIP1" />
		<KeyWord name="SKIP2" />
		<KeyWord name="SKIP3" />
		<KeyWord name="SORT" />
		<KeyWord name="SORT-CONTROL" />
		<KeyWord name="SORT-CORE-SIZE" />
		<KeyWord name="SORT-FILE-SIZE" />
		<KeyWord name="SORT-MERGE" />
		<KeyWord name="SORT-MESSAGE" />
		<KeyWord name="SORT-MODE-SIZE" />
		<KeyWord name="SORT-RETURN" />
		<KeyWord name="SOURCE" />
		<KeyWord name="SOURCE-COMPUTER" />
		<KeyWord name="SOURCES" />
		<KeyWord name="SPACE" />
		<KeyWord name="SPACES" />
		<KeyWord name="SPECIAL-NAMES" />
		<KeyWord name="SQL" />
		<KeyWord name="SQRT" func="yes">
			<Overload retVal="Numeric" descr="approximation of the square root of &lt;number&gt;">
				<Param name="number" />
			</Overload>
		</KeyWord>
		<KeyWord name="STANDARD" />
		<KeyWord name="STANDARD-1" />
		<KeyWord name="STANDARD-2" />
		<KeyWord name="STANDARD-BINARY" />
		<KeyWord name="STANDARD-COMPARE" func="yes">
			<Overload retVal="Alphanumeric" descr="comparision result of &lt;string-1&gt; and &lt;string-2&gt; using culturally-sensitive &lt;ordering-table&gt; with specified &lt;ordering-level&gt;">
				<Param name="string-1" />
				<Param name="string-2" />
				<Param name="[ordering-table, default: ISO14651_20xx_TABLE1]" />
				<Param name="[ordering-level, default: highest]" />
			</Overload>
		</KeyWord>
		<KeyWord name="STANDARD-DECIMAL" />
		<KeyWord name="STANDARD-DEVIATION" func="yes">
			<Overload retVal="Alphanumeric" descr="statistical standard deviation of the list of &lt;number&gt; arguments">
				<Param name="number" />
				<Param name="..." />
			</Overload>
		</KeyWord>
		<KeyWord name="START" />
		<KeyWord name="STATEMENT" />
		<KeyWord name="STATUS" />
		<KeyWord name="STDERR" />
		<KeyWord name="STDIN" />
		<KeyWord name="STDOUT" />
		<KeyWord name="STEP" />
		<KeyWord name="STOP" />
		<KeyWord name="STORED-CHAR-LENGTH" func="yes">
			<Overload retVal="Integer" descr="length (in bytes) of the specified &lt;string&gt;, minus the total number of trailing spaces">
				<Param name="string" />
			</Overload>
		</KeyWord>
		<KeyWord name="STRING" />
		<KeyWord name="STRONG" />
		<KeyWord name="STRUCTURE" />
		<KeyWord name="SUB-QUEUE-1" />
		<KeyWord name="SUB-QUEUE-2" />
		<KeyWord name="SUB-QUEUE-3" />
		<KeyWord name="SUBSTITUTE" func="yes">
			<Overload retVal="Alphanumeric/National" descr="specified &lt;string&gt;, replaced with all occurrences of the &lt;from&gt; strings (case-sensitive) with the corresponding &lt;to&gt; strings">
				<Param name="string" />
				<Param name="from" />
				<Param name="to" />
				<Param name="..." />
			</Overload>
		</KeyWord>
		<KeyWord name="SUBSTITUTE-CASE" func="yes">
			<Overload retVal="Alphanumeric/National" descr="specified &lt;string&gt;, replaced with all occurrences of the &lt;from&gt; strings (case-insensitive) with the corresponding &lt;to&gt; strings">
				<Param name="param" />
				<Param name="from" />
				<Param name="to" />
			</Overload>
		</KeyWord>
		<KeyWord name="SUBTRACT" />
		<KeyWord name="SUM" func="yes">
			<Overload retVal="Integer/Numeric" descr="sum of the &lt;number&gt; arguments">
				<Param name="number" />
				<Param name="..." />
			</Overload>
		</KeyWord>
		<KeyWord name="SUPER" />
		<KeyWord name="SUPPRESS" />
		<KeyWord name="SWITCH-0" />
		<KeyWord name="SWITCH-1" />
		<KeyWord name="SWITCH-10" />
		<KeyWord name="SWITCH-11" />
		<KeyWord name="SWITCH-12" />
		<KeyWord name="SWITCH-13" />
		<KeyWord name="SWITCH-14" />
		<KeyWord name="SWITCH-15" />
		<KeyWord name="SWITCH-16" />
		<KeyWord name="SWITCH-17" />
		<KeyWord name="SWITCH-18" />
		<KeyWord name="SWITCH-19" />
		<KeyWord name="SWITCH-2" />
		<KeyWord name="SWITCH-20" />
		<KeyWord name="SWITCH-21" />
		<KeyWord name="SWITCH-22" />
		<KeyWord name="SWITCH-23" />
		<KeyWord name="SWITCH-24" />
		<KeyWord name="SWITCH-25" />
		<KeyWord name="SWITCH-26" />
		<KeyWord name="SWITCH-27" />
		<KeyWord name="SWITCH-28" />
		<KeyWord name="SWITCH-29" />
		<KeyWord name="SWITCH-3" />
		<KeyWord name="SWITCH-30" />
		<KeyWord name="SWITCH-31" />
		<KeyWord name="SWITCH-32" />
		<KeyWord name="SWITCH-33" />
		<KeyWord name="SWITCH-34" />
		<KeyWord name="SWITCH-35" />
		<KeyWord name="SWITCH-36" />
		<KeyWord name="SWITCH-4" />
		<KeyWord name="SWITCH-5" />
		<KeyWord name="SWITCH-6" />
		<KeyWord name="SWITCH-7" />
		<KeyWord name="SWITCH-8" />
		<KeyWord name="SWITCH-9" />
		<KeyWord name="SYMBOL" />
		<KeyWord name="SYMBOLIC" />
		<KeyWord name="SYNC" />
		<KeyWord name="SYNCHRONIZED" />
		<KeyWord name="SYSERR" />
		<KeyWord name="SYSIN" />
		<KeyWord name="SYSIPT" />
		<KeyWord name="SYSLIST" />
		<KeyWord name="SYSLST" />
		<KeyWord name="SYSOUT" />
		<KeyWord name="SYSTEM-DEFAULT" />
		<KeyWord name="TAB" />
		<KeyWord name="TABLE" />
		<KeyWord name="TALLY" />
		<KeyWord name="TALLYING" />
		<KeyWord name="TAN" func="yes">
			<Overload retVal="Numeric" descr="trigonometric tangent of the specified &lt;angle&gt;">
				<Param name="angle" />
			</Overload>
		</KeyWord>
		<KeyWord name="TAPE" />
		<KeyWord name="TERMINAL" />
		<KeyWord name="TERMINATE" />
		<KeyWord name="TEST" />
		<KeyWord name="TEST-DATE-YYYYMMDD" func="yes">
			<Overload retVal="Integer" descr="check if supplied &lt;yyyyddd&gt; is valid, returns 0 (=ok), 1 (=error year), 2 (=error month) or 3 (=error date)">
				<Param name="yyyymmdd" />
			</Overload>
		</KeyWord>
		<KeyWord name="TEST-DAY-YYYYDDD" func="yes">
			<Overload retVal="Integer" descr="check if supplied &lt;yyyyddd&gt; is valid, returns 0 (=ok), 1 (=error year), 2 (=error day)">
				<Param name="yyyyddd" />
			</Overload>
		</KeyWord>
		<KeyWord name="TEST-FORMATTED-DATETIME" func="yes">
			<Overload retVal="Integer" descr="check whether &lt;data-item&gt; is valid according to the specified &lt;format&gt; (a date, a time, or a combined date and time format), returns 0 if valid, character-position otherwise">
				<Param name="format" />
				<Param name="data-item" />
			</Overload>
		</KeyWord>
		<KeyWord name="TEST-NUMVAL" func="yes">
			<Overload retVal="Integer" descr="check if &lt;string&gt; is appropriate for use as the &lt;string&gt; argument for NUMVAL, returns 0 (=ok) or error position">
				<Param name="string" />
			</Overload>
		</KeyWord>
		<KeyWord name="TEST-NUMVAL-C" func="yes">
			<Overload retVal="Integer" descr="check if &lt;string&gt; is appropriate for use as the &lt;string&gt; argument for NUMVAL-C, case-sensitive if ANYCASE not given, returns 0 (=ok) or error position">
				<Param name="string (may contain currency-symbol)" />
				<Param name="[currency-symbol if not default | LOCALE [locale-name] ]" />
				<Param name="[ANYCASE]" />
			</Overload>
		</KeyWord>
		<KeyWord name="TEST-NUMVAL-F" func="yes">
			<Overload retVal="Integer" descr="check if &lt;string&gt; is appropriate for use as the &lt;string&gt; argument for NUMVAL-F, returns 0 (=ok) or error position">
				<Param name="string" />
			</Overload>
		</KeyWord>
		<KeyWord name="TEXT" />
		<KeyWord name="THAN" />
		<KeyWord name="THEN" />
		<KeyWord name="THROUGH" />
		<KeyWord name="THRU" />
		<KeyWord name="TIME" />
		<KeyWord name="TIMES" />
		<KeyWord name="TO" />
		<KeyWord name="TOP" />
		<KeyWord name="TOWARD-GREATER" />
		<KeyWord name="TOWARD-LESSER" />
		<KeyWord name="TRACK-THUMB" />
		<KeyWord name="TRAILING" />
		<KeyWord name="TRANSFORM" />
		<KeyWord name="TRIM" func="yes">
			<Overload retVal="Alphanumeric/National" descr="remove leading or trailing spaces, or both, from &lt;string&gt;">
				<Param name="string" />
				<Param name="[LEADING or TRAILING, default: both]" />
			</Overload>
		</KeyWord>
		<KeyWord name="TRUE" />
		<KeyWord name="TRUNCATION" />
		<KeyWord name="TYPE" />
		<KeyWord name="TYPEDEF" />
		<!-- <KeyWord name="U" /> -->
		<KeyWord name="UCS-4" />
		<KeyWord name="UNBOUNDED" />
		<KeyWord name="UNDERLINE" />
		<KeyWord name="UNIT" />
		<KeyWord name="UNIVERSAL" />
		<KeyWord name="UNLOCK" />
		<KeyWord name="UNSIGNED" />
		<KeyWord name="UNSTRING" />
		<KeyWord name="UNTIL" />
		<KeyWord name="UP" />
		<KeyWord name="UPON" />
		<KeyWord name="UPPER-CASE" func="yes">
			<Overload retVal="Alphanumeric/National" descr="character string that contains &lt;string&gt; with any lowercase letters replaced by their corresponding uppercase letters">
				<Param name="string" />
			</Overload>
		</KeyWord>
		<KeyWord name="UPSI-0" />
		<KeyWord name="UPSI-1" />
		<KeyWord name="UPSI-2" />
		<KeyWord name="UPSI-3" />
		<KeyWord name="UPSI-4" />
		<KeyWord name="UPSI-5" />
		<KeyWord name="UPSI-6" />
		<KeyWord name="UPSI-7" />
		<KeyWord name="USAGE" />
		<KeyWord name="USE" />
		<KeyWord name="USER-DEFAULT" />
		<KeyWord name="USING" />
		<KeyWord name="UTF-16" />
		<KeyWord name="UTF-8" />
		<!-- <KeyWord name="V" /> -->
		<KeyWord name="VAL-STATUS" />
		<KeyWord name="VALID" />
		<KeyWord name="VALIDATE" />
		<KeyWord name="VALIDATE-STATUS" />
		<KeyWord name="VALUE" />
		<KeyWord name="VALUES" />
		<KeyWord name="VARIANCE" func="yes">
			<Overload retVal="Numeric" descr="statistical variance of the specified list of &lt;number&gt; arguments">
				<Param name="number" />
				<Param name="..." />
			</Overload>
		</KeyWord>
		<KeyWord name="VARIANT" />
		<KeyWord name="VARYING" />
		<KeyWord name="VIRTUAL-WIDTH" />
		<KeyWord name="VSCROLL" />
		<KeyWord name="VSCROLL-BAR" />
		<KeyWord name="VSCROLL-POS" />
		<KeyWord name="WAIT" />
		<KeyWord name="WHEN" />
		<KeyWord name="WHEN-COMPILED" func="yes">
			<Overload retVal="Alphanumeric" descr="date and time the compilation unit was compiled in format yyyymmddhhmmssnnooooo"/>
		</KeyWord>
		<KeyWord name="WIDTH" />
		<KeyWord name="WITH" />
		<KeyWord name="WORDS" />
		<KeyWord name="WORKING-STORAGE" />
		<KeyWord name="WRAP" />
		<KeyWord name="WRITE" />
		<!-- <KeyWord name="X" /> -->
		<KeyWord name="XML" />
		<KeyWord name="XML-CODE" />
		<KeyWord name="XML-EVENT" />
		<KeyWord name="XML-INFORMATION" />
		<KeyWord name="XML-NAMESPACE" />
		<KeyWord name="XML-NAMESPACE-PREFIX" />
		<KeyWord name="XML-NNAMESPACE" />
		<KeyWord name="XML-NNAMESPACE-PREFIX" />
		<KeyWord name="XML-NTEXT" />
		<KeyWord name="XML-TEXT" />
		<!-- <KeyWord name="Y" /> -->
		<KeyWord name="YEAR-TO-YYYY" func="yes">
			<Overload retVal="Integer" descr="convert yy to yyyy with optional &lt;yy-cutoff&gt; to delineate centuries">
				<Param name="yy" />
				<Param name="[yy-cutoff, default: 50]" />
				<Param name="[yy-execution-time, default: now]" />
			</Overload>
		</KeyWord>
		<KeyWord name="YYYYDDD" />
		<KeyWord name="YYYYMMDD" />
		<KeyWord name="ZERO" />
		<KeyWord name="ZEROES" />
		<KeyWord name="ZEROS" />
	</AutoComplete>
</NotepadPlus>
