@echo off
chcp 65001 >nul
title 测试下载脚本
echo ========================================
echo 测试下载脚本
echo ========================================
echo.

echo 正在测试PowerShell命令...
powershell -Command "Write-Host '✓ PowerShell 工作正常'"

echo.
echo 正在创建测试目录...
if not exist TestDownloads mkdir TestDownloads
cd TestDownloads

echo.
echo 测试下载一个小文件...
powershell -Command "Invoke-WebRequest -Uri 'https://www.google.com/robots.txt' -OutFile 'test.txt'"

if exist "test.txt" (
    echo ✓ 下载测试成功！
    echo 文件内容：
    type test.txt
) else (
    echo ✗ 下载测试失败
)

echo.
echo 清理测试文件...
cd ..
rmdir /s /q TestDownloads 2>nul

echo.
echo 测试完成！
pause
