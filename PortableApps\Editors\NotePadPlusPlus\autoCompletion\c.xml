<?xml version="1.0" encoding="UTF-8" ?>
<NotepadPlus>
	<AutoComplete language="C">
		<Environment ignoreCase="no" startFunc="(" stopFunc=")" paramSeparator="," terminal=";" />
		<KeyWord name="#define" />
		<KeyWord name="#elif" />
		<KeyWord name="#else" />
		<KeyWord name="#endif" />
		<KeyWord name="#error" />
		<KeyWord name="#if" />
		<KeyWord name="#ifdef" />
		<KeyWord name="#ifndef" />
		<KeyWord name="#include" />
		<KeyWord name="#line" />
		<KeyWord name="#pragma" />
		<KeyWord name="#undef" />
		<KeyWord name="abort" func="yes">
			<Overload retVal="void" >
				<Param name="void" />
			</Overload>
		</KeyWord>
		<KeyWord name="abs" func="yes">
			<Overload retVal="int" >
				<Param name="int i" />
			</Overload>
		</KeyWord>
		<KeyWord name="absread" />
		<KeyWord name="abswrite" />
		<KeyWord name="access" func="yes">
			<Overload retVal="int" >
				<Param name="const char *path" />
				<Param name="int amode" />
			</Overload>
		</KeyWord>
		<KeyWord name="acos" func="yes">
			<Overload retVal="double" >
				<Param name="double x" />
			</Overload>
		</KeyWord>
		<KeyWord name="acosl" />
		<KeyWord name="allocmem" />
		<KeyWord name="arc" />
		<KeyWord name="arg" />
		<KeyWord name="asctime" func="yes">
			<Overload retVal="char*" >
				<Param name="const struct tm *timeptr" />
			</Overload>
		</KeyWord>
		<KeyWord name="asin" func="yes">
			<Overload retVal="double" >
				<Param name="double x" />
			</Overload>
		</KeyWord>
		<KeyWord name="asinl" />
		<KeyWord name="assert" func="yes">
			<Overload retVal="void" >
				<Param name="int expression" />
			</Overload>
		</KeyWord>
		<KeyWord name="atan" func="yes">
			<Overload retVal="double" >
				<Param name="double x" />
			</Overload>
		</KeyWord>
		<KeyWord name="atan2" func="yes">
			<Overload retVal="double" >
				<Param name="double y" />
				<Param name="double x" />
			</Overload>
		</KeyWord>
		<KeyWord name="atan2l" />
		<KeyWord name="atanl" />
		<KeyWord name="atexit" func="yes">
			<Overload retVal="int" >
				<Param name="void (*func)(void)" />
			</Overload>
		</KeyWord>
		<KeyWord name="atof" func="yes">
			<Overload retVal="double" >
				<Param name="const char *nptr" />
			</Overload>
		</KeyWord>
		<KeyWord name="atoi" func="yes">
			<Overload retVal="int" >
				<Param name="const char *nptr" />
			</Overload>
		</KeyWord>
		<KeyWord name="atol" func="yes">
			<Overload retVal="long int" >
				<Param name="const char *nptr" />
			</Overload>
		</KeyWord>
		<KeyWord name="auto" />
		<KeyWord name="bar" />
		<KeyWord name="bar3d" />
		<KeyWord name="bcd" />
		<KeyWord name="bdos" />
		<KeyWord name="bdosptr" />
		<KeyWord name="bioscom" />
		<KeyWord name="biosdisk" />
		<KeyWord name="biosequip" />
		<KeyWord name="bioskey" />
		<KeyWord name="biosmemory" />
		<KeyWord name="biosprint" />
		<KeyWord name="biostime" />
		<KeyWord name="break" />
		<KeyWord name="brk" />
		<KeyWord name="bsearch" func="yes">
			<Overload retVal="void *" >
				<Param name="const void *key" />
				<Param name="const void *base" />
				<Param name="size_t nmemb" />
				<Param name="size_t size" />
				<Param name="int (*compar)(const void *, const void *)" />
			</Overload>
		</KeyWord>
		<KeyWord name="cabs" />
		<KeyWord name="cabsl" />
		<KeyWord name="calloc" func="yes">
			<Overload retVal="void *" >
				<Param name="size_t nmemb" />
				<Param name="size_t size" />
			</Overload>
		</KeyWord>
		<KeyWord name="case" />
		<KeyWord name="ceil" func="yes">
			<Overload retVal="double" >
				<Param name="double x" />
			</Overload>
		</KeyWord>
		<KeyWord name="ceill" />
		<KeyWord name="cerr" />
		<KeyWord name="cgets" />
		<KeyWord name="char" />
		<KeyWord name="chdir" func="yes">
			<Overload retVal="int" >
				<Param name="const char *path" />
			</Overload>
		</KeyWord>
		<KeyWord name="chmod" func="yes">
			<Overload retVal="int" >
				<Param name="const char *path" />
				<Param name="mode_t mode" />
			</Overload>
		</KeyWord>
		<KeyWord name="chsize" />
		<KeyWord name="cin" />
		<KeyWord name="circle" />
		<KeyWord name="cleardevice" />
		<KeyWord name="clearerr" func="yes">
			<Overload retVal="void" >
				<Param name="FILE *stream" />
			</Overload>
		</KeyWord>
		<KeyWord name="clearviewport" />
		<KeyWord name="clock" func="yes">
			<Overload retVal="clock_t" >
				<Param name="void" />
			</Overload>
		</KeyWord>
		<KeyWord name="close" func="yes">
			<Overload retVal="int" >
				<Param name="int filedes" />
			</Overload>
		</KeyWord>
		<KeyWord name="closedir" func="yes">
			<Overload retVal="int" >
				<Param name="DIR *dirp" />
			</Overload>
		</KeyWord>
		<KeyWord name="closegraph" />
		<KeyWord name="clreol" />
		<KeyWord name="clrscr" />
		<KeyWord name="complex" />
		<KeyWord name="conj" />
		<KeyWord name="const" />
		<KeyWord name="continue" />
		<KeyWord name="coreleft" />
		<KeyWord name="cos" func="yes">
			<Overload retVal="double" >
				<Param name="double x" />
			</Overload>
		</KeyWord>
		<KeyWord name="cosh" func="yes">
			<Overload retVal="double" >
				<Param name="double x" />
			</Overload>
		</KeyWord>
		<KeyWord name="coshl" />
		<KeyWord name="cosl" />
		<KeyWord name="country" />
		<KeyWord name="cprintf" />
		<KeyWord name="cputs" />
		<KeyWord name="creat" func="yes">
			<Overload retVal="int" >
				<Param name="const char *path" />
				<Param name="mode_t mode" />
			</Overload>
		</KeyWord>
		<KeyWord name="creatnew" />
		<KeyWord name="creattemp" />
		<KeyWord name="cscanf" />
		<KeyWord name="ctime" func="yes">
			<Overload retVal="char *" >
				<Param name="const time_t *timer" />
			</Overload>
		</KeyWord>
		<KeyWord name="ctrlbrk" />
		<KeyWord name="default" />
		<KeyWord name="delay" />
		<KeyWord name="delline" />
		<KeyWord name="detectgraph" />
		<KeyWord name="difftime" func="yes">
			<Overload retVal="double" >
				<Param name="time_t time1" />
				<Param name="time_t time0" />
			</Overload>
		</KeyWord>
		<KeyWord name="disable" />
		<KeyWord name="div" func="yes">
			<Overload retVal="div_t" >
				<Param name="int numer" />
				<Param name="int denom" />
			</Overload>
		</KeyWord>
		<KeyWord name="dllexport" />
		<KeyWord name="dllexport2" />
		<KeyWord name="dllimport" />
		<KeyWord name="dllimport2" />
		<KeyWord name="do" />
		<KeyWord name="dosexterr" />
		<KeyWord name="dostounix" />
		<KeyWord name="double" />
		<KeyWord name="drawpoly" />
		<KeyWord name="dup" func="yes">
			<Overload retVal="int" >
				<Param name="int filedes" />
			</Overload>
		</KeyWord>
		<KeyWord name="dup2" func="yes">
			<Overload retVal="int" >
				<Param name="int filedes" />
				<Param name="int filedes2" />
			</Overload>
		</KeyWord>
		<KeyWord name="ecvt" />
		<KeyWord name="ellipse" />
		<KeyWord name="else" />
		<KeyWord name="enable" />
		<KeyWord name="enum" />
		<KeyWord name="eof" />
		<KeyWord name="execl" func="yes">
			<Overload retVal="int" >
				<Param name="const char *path" />
				<Param name="const char *args" />
				<Param name="..." />
			</Overload>
		</KeyWord>
		<KeyWord name="execle" func="yes">
			<Overload retVal="int" >
				<Param name="const char *path" />
				<Param name="const char *args" />
				<Param name="..." />
			</Overload>
		</KeyWord>
		<KeyWord name="execlp" func="yes">
			<Overload retVal="int" >
				<Param name="const char *file" />
				<Param name="const char *args" />
				<Param name="..." />
			</Overload>
		</KeyWord>
		<KeyWord name="execlpe" />
		<KeyWord name="execv" func="yes">
			<Overload retVal="int" >
				<Param name="const char *path" />
				<Param name="char *const argv[]" />
			</Overload>
		</KeyWord>
		<KeyWord name="execve" func="yes">
			<Overload retVal="int" >
				<Param name="const char *path" />
				<Param name="char *const argv[]" />
				<Param name="char *const *envp" />
			</Overload>
		</KeyWord>
		<KeyWord name="execvp" func="yes">
			<Overload retVal="int" >
				<Param name="const char *file" />
				<Param name="char *const argv[]" />
			</Overload>
		</KeyWord>
		<KeyWord name="execvpe" />
		<KeyWord name="exit" func="yes">
			<Overload retVal="void" >
				<Param name="int status" />
			</Overload>
		</KeyWord>
		<KeyWord name="exp" func="yes">
			<Overload retVal="double" >
				<Param name="double x" />
			</Overload>
		</KeyWord>
		<KeyWord name="expl" />
		<KeyWord name="extern" />
		<KeyWord name="fabs" func="yes">
			<Overload retVal="double" >
				<Param name="double x" />
			</Overload>
		</KeyWord>
		<KeyWord name="fabsl" />
		<KeyWord name="farcalloc" />
		<KeyWord name="farcoreleft" />
		<KeyWord name="farfree" />
		<KeyWord name="farheapcheck" />
		<KeyWord name="farheapcheckfree" />
		<KeyWord name="farheapchecknode" />
		<KeyWord name="farheapfillfree" />
		<KeyWord name="farheapwalk" />
		<KeyWord name="farmalloc" />
		<KeyWord name="farrealloc" />
		<KeyWord name="fclose" func="yes">
			<Overload retVal="int" >
				<Param name="File *stream" />
			</Overload>
		</KeyWord>
		<KeyWord name="fcloseall" />
		<KeyWord name="fcvt" />
		<KeyWord name="fdopen" func="yes">
			<Overload retVal="File *" >
				<Param name="int filedes" />
				<Param name="const char *type" />
			</Overload>
		</KeyWord>
		<KeyWord name="feof" func="yes">
			<Overload retVal="int" >
				<Param name="FILE *stream" />
			</Overload>
		</KeyWord>
		<KeyWord name="ferror" func="yes">
			<Overload retVal="int" >
				<Param name="FILE *stream" />
			</Overload>
		</KeyWord>
		<KeyWord name="fflush" func="yes">
			<Overload retVal="int" >
				<Param name="FILE *stream" />
			</Overload>
		</KeyWord>
		<KeyWord name="fgetc" func="yes">
			<Overload retVal="int" >
				<Param name="FILE *stream" />
			</Overload>
		</KeyWord>
		<KeyWord name="fgetchar" />
		<KeyWord name="fgetpos" func="yes">
			<Overload retVal="int" >
				<Param name="FILE *stream" />
				<Param name="fpos_t *pos" />
			</Overload>
		</KeyWord>
		<KeyWord name="fgets" func="yes">
			<Overload retVal="char *" >
				<Param name="char *s" />
				<Param name="int n" />
				<Param name="FILE *stream" />
			</Overload>
		</KeyWord>
		<KeyWord name="filelength" />
		<KeyWord name="fileno" func="yes">
			<Overload retVal="int" >
				<Param name="FILE *stream" />
			</Overload>
		</KeyWord>
		<KeyWord name="fillellipse" />
		<KeyWord name="fillpoly" />
		<KeyWord name="findfirst" />
		<KeyWord name="findnext" />
		<KeyWord name="float" />
		<KeyWord name="floodfill" />
		<KeyWord name="floor" func="yes">
			<Overload retVal="double" >
				<Param name="double x" />
			</Overload>
		</KeyWord>
		<KeyWord name="floorl" />
		<KeyWord name="flushall" />
		<KeyWord name="fmod" func="yes">
			<Overload retVal="double" >
				<Param name="double x" />
				<Param name="double y" />
			</Overload>
		</KeyWord>
		<KeyWord name="fmodl" />
		<KeyWord name="fnmerge" />
		<KeyWord name="fnsplit" />
		<KeyWord name="fopen" func="yes">
			<Overload retVal="FILE *" >
				<Param name="const char *filename" />
				<Param name="const char *mode" />
			</Overload>
		</KeyWord>
		<KeyWord name="for" />
		<KeyWord name="FP_OFF" />
		<KeyWord name="FP_SEG" />
		<KeyWord name="fprintf" func="yes">
			<Overload retVal="int" >
				<Param name="FILE *stream" />
				<Param name="const char *format" />
				<Param name="..." />
			</Overload>
		</KeyWord>
		<KeyWord name="fputc" func="yes">
			<Overload retVal="int" >
				<Param name="int c" />
				<Param name="FILE *stream" />
			</Overload>
		</KeyWord>
		<KeyWord name="fputchar" />
		<KeyWord name="fputs" func="yes">
			<Overload retVal="int" >
				<Param name="const char *s" />
				<Param name="FILE *stream" />
			</Overload>
		</KeyWord>
		<KeyWord name="fread" func="yes">
			<Overload retVal="size_t" >
				<Param name="void *ptr" />
				<Param name="size_t size" />
				<Param name="size_t nmemb" />
				<Param name="FILE *stream" />
			</Overload>
		</KeyWord>
		<KeyWord name="free" func="yes">
			<Overload retVal="void" >
				<Param name="void *ptr" />
			</Overload>
		</KeyWord>
		<KeyWord name="freemem" />
		<KeyWord name="freopen" func="yes">
			<Overload retVal="FILE *" >
				<Param name="const char *filename" />
				<Param name="const char *mode" />
				<Param name="FILE *stream" />
			</Overload>
		</KeyWord>
		<KeyWord name="frexp" func="yes">
			<Overload retVal="double" >
				<Param name="double value" />
				<Param name="int *exp" />
			</Overload>
		</KeyWord>
		<KeyWord name="frexpl" />
		<KeyWord name="friend" />
		<KeyWord name="fscanf" func="yes">
			<Overload retVal="int" >
				<Param name="FILE *stream" />
				<Param name="const char *format" />
				<Param name="..." />
			</Overload>
		</KeyWord>
		<KeyWord name="fseek" func="yes">
			<Overload retVal="int" >
				<Param name="FILE *stream" />
				<Param name="long int offset" />
				<Param name="int whence" />
			</Overload>
		</KeyWord>
		<KeyWord name="fsetpos" func="yes">
			<Overload retVal="int" >
				<Param name="FILE *stream" />
				<Param name="const fpos_t * pos" />
			</Overload>
		</KeyWord>
		<KeyWord name="fstat" func="yes">
			<Overload retVal="int" >
				<Param name="int filedes" />
				<Param name="struct stat *buf" />
			</Overload>
		</KeyWord>
		<KeyWord name="ftell" func="yes">
			<Overload retVal="long int" >
				<Param name="FILE *stream" />
			</Overload>
		</KeyWord>
		<KeyWord name="ftime" />
		<KeyWord name="fwrite" func="yes">
			<Overload retVal="size_t" >
				<Param name="const void *ptr" />
				<Param name="size_t size" />
				<Param name="size_t nmemb" />
				<Param name="FILE *stream" />
			</Overload>
		</KeyWord>
		<KeyWord name="gcvt" />
		<KeyWord name="geninterrupt" />
		<KeyWord name="getarccoords" />
		<KeyWord name="getaspectratio" />
		<KeyWord name="getbkcolor" />
		<KeyWord name="getc" func="yes">
			<Overload retVal="int" >
				<Param name="FILE *stream" />
			</Overload>
		</KeyWord>
		<KeyWord name="getcbrk" />
		<KeyWord name="getch" />
		<KeyWord name="getchar" func="yes">
			<Overload retVal="int" >
				<Param name="void" />
			</Overload>
		</KeyWord>
		<KeyWord name="getche" />
		<KeyWord name="getcolor" />
		<KeyWord name="getcurdir" />
		<KeyWord name="getcwd" func="yes">
			<Overload retVal="char *" >
				<Param name="char *buf" />
				<Param name="size_t size" />
			</Overload>
		</KeyWord>
		<KeyWord name="getdate" />
		<KeyWord name="getdefaultpalette" />
		<KeyWord name="getdfree" />
		<KeyWord name="getdisk" />
		<KeyWord name="getdrivername" />
		<KeyWord name="getdta" />
		<KeyWord name="getenv" func="yes">
			<Overload retVal="char *" >
				<Param name="const char *name" />
			</Overload>
		</KeyWord>
		<KeyWord name="getfat" />
		<KeyWord name="getfatd" />
		<KeyWord name="getfillpattern" />
		<KeyWord name="getfillsettings" />
		<KeyWord name="getftime" />
		<KeyWord name="getgraphmode" />
		<KeyWord name="getimage" />
		<KeyWord name="getlinesettings" />
		<KeyWord name="getmaxcolor" />
		<KeyWord name="getmaxmode" />
		<KeyWord name="getmaxx" />
		<KeyWord name="getmaxy" />
		<KeyWord name="getmodename" />
		<KeyWord name="getmoderange" />
		<KeyWord name="getpalette" />
		<KeyWord name="getpalettesize" />
		<KeyWord name="getpass" />
		<KeyWord name="getpid" func="yes">
			<Overload retVal="pid_t" >
				<Param name="void" />
			</Overload>
		</KeyWord>
		<KeyWord name="getpixel" />
		<KeyWord name="getpsp" />
		<KeyWord name="gets" func="yes">
			<Overload retVal="char *" >
				<Param name="char *s" />
			</Overload>
		</KeyWord>
		<KeyWord name="gettext" />
		<KeyWord name="gettextinfo" />
		<KeyWord name="gettextsettings" />
		<KeyWord name="gettime" />
		<KeyWord name="getvect" />
		<KeyWord name="getverify" />
		<KeyWord name="getviewsettings" />
		<KeyWord name="getw" />
		<KeyWord name="getx" />
		<KeyWord name="gety" />
		<KeyWord name="gmtime" func="yes">
			<Overload retVal="struct tm *" >
				<Param name="const time_t *timer" />
			</Overload>
		</KeyWord>
		<KeyWord name="goto" />
		<KeyWord name="gotoxy" />
		<KeyWord name="graphdefaults" />
		<KeyWord name="grapherrormsg" />
		<KeyWord name="graphresult" />
		<KeyWord name="harderr" />
		<KeyWord name="hardresume" />
		<KeyWord name="hardretn" />
		<KeyWord name="heapcheck" />
		<KeyWord name="heapcheckfree" />
		<KeyWord name="heapchecknode" />
		<KeyWord name="heapfillfree" />
		<KeyWord name="heapwalk" />
		<KeyWord name="highvideo" />
		<KeyWord name="hypot" />
		<KeyWord name="hypotl" />
		<KeyWord name="if" />
		<KeyWord name="imag" />
		<KeyWord name="imagesize" />
		<KeyWord name="initgraph" />
		<KeyWord name="inp" />
		<KeyWord name="inport" />
		<KeyWord name="inportb" />
		<KeyWord name="inpw" />
		<KeyWord name="insline" />
		<KeyWord name="installuserdriver" />
		<KeyWord name="installuserfont" />
		<KeyWord name="int" />
		<KeyWord name="int86" />
		<KeyWord name="int86x" />
		<KeyWord name="intdos" />
		<KeyWord name="intdosx" />
		<KeyWord name="intr" />
		<KeyWord name="ioctl" />
		<KeyWord name="isalnum" func="yes">
			<Overload retVal="int" >
				<Param name="int c" />
			</Overload>
		</KeyWord>
		<KeyWord name="isalpha" func="yes">
			<Overload retVal="int" >
				<Param name="int c" />
			</Overload>
		</KeyWord>
		<KeyWord name="isascii" />
		<KeyWord name="isatty" func="yes">
			<Overload retVal="int" >
				<Param name="int filedes" />
			</Overload>
		</KeyWord>
		<KeyWord name="iscntrl" func="yes">
			<Overload retVal="int" >
				<Param name="int c" />
			</Overload>
		</KeyWord>
		<KeyWord name="isdigit" func="yes">
			<Overload retVal="int" >
				<Param name="int c" />
			</Overload>
		</KeyWord>
		<KeyWord name="isgraph" func="yes">
			<Overload retVal="int" >
				<Param name="int c" />
			</Overload>
		</KeyWord>
		<KeyWord name="islower" func="yes">
			<Overload retVal="int" >
				<Param name="int c" />
			</Overload>
		</KeyWord>
		<KeyWord name="isprint" func="yes">
			<Overload retVal="int" >
				<Param name="int c" />
			</Overload>
		</KeyWord>
		<KeyWord name="ispunct" func="yes">
			<Overload retVal="int" >
				<Param name="int c" />
			</Overload>
		</KeyWord>
		<KeyWord name="isspace" func="yes">
			<Overload retVal="int" >
				<Param name="int c" />
			</Overload>
		</KeyWord>
		<KeyWord name="isupper" func="yes">
			<Overload retVal="int" >
				<Param name="int c" />
			</Overload>
		</KeyWord>
		<KeyWord name="isxdigit" func="yes">
			<Overload retVal="int" >
				<Param name="int c" />
			</Overload>
		</KeyWord>
		<KeyWord name="itoa" />
		<KeyWord name="kbhit" />
		<KeyWord name="keep" />
		<KeyWord name="labs" func="yes">
			<Overload retVal="long int" >
				<Param name="long int i" />
			</Overload>
		</KeyWord>
		<KeyWord name="ldexp" func="yes">
			<Overload retVal="double" >
				<Param name="double x" />
				<Param name="int exp" />
			</Overload>
		</KeyWord>
		<KeyWord name="ldexpl" />
		<KeyWord name="ldiv" func="yes">
			<Overload retVal="ldiv_t" >
				<Param name="long int numer" />
				<Param name="long int denom" />
			</Overload>
		</KeyWord>
		<KeyWord name="lfind" />
		<KeyWord name="line" />
		<KeyWord name="linerel" />
		<KeyWord name="lineto" />
		<KeyWord name="localeconv" func="yes">
			<Overload retVal="struct lconv *" >
				<Param name="void" />
			</Overload>
		</KeyWord>
		<KeyWord name="localtime" func="yes">
			<Overload retVal="struct tm *" >
				<Param name="const time_t *timer" />
			</Overload>
		</KeyWord>
		<KeyWord name="lock" />
		<KeyWord name="locking" />
		<KeyWord name="log" func="yes">
			<Overload retVal="double" >
				<Param name="double x" />
			</Overload>
		</KeyWord>
		<KeyWord name="log10" func="yes">
			<Overload retVal="double" >
				<Param name="double x" />
			</Overload>
		</KeyWord>
		<KeyWord name="log10l" />
		<KeyWord name="logl" />
		<KeyWord name="long" />
		<KeyWord name="longjmp" func="yes">
			<Overload retVal="void" >
				<Param name="jmp_buf env" />
				<Param name="int val" />
			</Overload>
		</KeyWord>
		<KeyWord name="lowvideo" />
		<KeyWord name="lsearch" />
		<KeyWord name="lseek" func="yes">
			<Overload retVal="off_t" >
				<Param name="int filedes" />
				<Param name="off_t offset" />
				<Param name="int whence" />
			</Overload>
		</KeyWord>
		<KeyWord name="ltoa" />
		<KeyWord name="malloc" func="yes">
			<Overload retVal="void" >
				<Param name="size_t size" />
			</Overload>
		</KeyWord>
		<KeyWord name="matherr" />
		<KeyWord name="max" />
		<KeyWord name="mblen" func="yes">
			<Overload retVal="int" >
				<Param name="const char *s" />
				<Param name="size_t n" />
			</Overload>
		</KeyWord>
		<KeyWord name="mbstowcs" func="yes">
			<Overload retVal="size_t" >
				<Param name="wchar_t *pwcs" />
				<Param name="const char *s" />
				<Param name="size_t n" />
			</Overload>
		</KeyWord>
		<KeyWord name="mbtowc" func="yes">
			<Overload retVal="int" >
				<Param name="wchar_t *pwc" />
				<Param name="const char *s" />
				<Param name="size_t n" />
			</Overload>
		</KeyWord>
		<KeyWord name="memccpy" />
		<KeyWord name="memchr" func="yes">
			<Overload retVal="void *" >
				<Param name="const void *s" />
				<Param name="int c" />
				<Param name="size_t n" />
			</Overload>
		</KeyWord>
		<KeyWord name="memcmp" func="yes">
			<Overload retVal="int" >
				<Param name="const void *s1" />
				<Param name="const void *s2" />
				<Param name="size_t n" />
			</Overload>
		</KeyWord>
		<KeyWord name="memcpy" func="yes">
			<Overload retVal="void *" >
				<Param name="void *s1" />
				<Param name="const void *s2" />
				<Param name="size_t n" />
			</Overload>
		</KeyWord>
		<KeyWord name="memicmp" />
		<KeyWord name="memmove" func="yes">
			<Overload retVal="void *" >
				<Param name="void * s1" />
				<Param name="const void *s2" />
				<Param name="size_t n" />
			</Overload>
		</KeyWord>
		<KeyWord name="memset" func="yes">
			<Overload retVal="void *" >
				<Param name="void *s" />
				<Param name="int c" />
				<Param name="size_t n" />
			</Overload>
		</KeyWord>
		<KeyWord name="min" />
		<KeyWord name="MK_FP" />
		<KeyWord name="mkdir" func="yes">
			<Overload retVal="int" >
				<Param name="const char *path" />
				<Param name="mode_t mode" />
			</Overload>
		</KeyWord>
		<KeyWord name="mktemp" />
		<KeyWord name="mktime" func="yes">
			<Overload retVal="time_t" >
				<Param name="struct tm *timer" />
			</Overload>
		</KeyWord>
		<KeyWord name="modf" func="yes">
			<Overload retVal="double" >
				<Param name="double value" />
				<Param name="double *iptr" />
			</Overload>
		</KeyWord>
		<KeyWord name="modfl" />
		<KeyWord name="movedata" />
		<KeyWord name="moverel" />
		<KeyWord name="movetext" />
		<KeyWord name="moveto" />
		<KeyWord name="movmem" />
		<KeyWord name="namespace" />
		<KeyWord name="norm" />
		<KeyWord name="normvideo" />
		<KeyWord name="nosound" />
		<KeyWord name="open" func="yes">
			<Overload retVal="int" >
				<Param name="const char *path" />
				<Param name="int oflag" />
				<Param name="..." />
			</Overload>
		</KeyWord>
		<KeyWord name="opendir" func="yes">
			<Overload retVal="DIR *" >
				<Param name="const char *dirname" />
			</Overload>
		</KeyWord>
		<KeyWord name="operator" />
		<KeyWord name="outp" />
		<KeyWord name="outport" />
		<KeyWord name="outportb" />
		<KeyWord name="outpw" />
		<KeyWord name="outtext" />
		<KeyWord name="outtextxy" />
		<KeyWord name="parsfnm" />
		<KeyWord name="peek" />
		<KeyWord name="peekb" />
		<KeyWord name="perror" func="yes">
			<Overload retVal="void" >
				<Param name="const char *s" />
			</Overload>
		</KeyWord>
		<KeyWord name="pieslice" />
		<KeyWord name="poke" />
		<KeyWord name="pokeb" />
		<KeyWord name="polar" />
		<KeyWord name="poly" />
		<KeyWord name="polyl" />
		<KeyWord name="pow" func="yes">
			<Overload retVal="double" >
				<Param name="double x" />
				<Param name="double y" />
			</Overload>
		</KeyWord>
		<KeyWord name="pow10" />
		<KeyWord name="pow10l" />
		<KeyWord name="powl" />
		<KeyWord name="printf" func="yes">
			<Overload retVal="int" >
				<Param name="const char *format" />
				<Param name="..." />
			</Overload>
		</KeyWord>
		<KeyWord name="private" />
		<KeyWord name="protected" />
		<KeyWord name="public" />
		<KeyWord name="putc" func="yes">
			<Overload retVal="int" >
				<Param name="int c" />
				<Param name="FILE *stream" />
			</Overload>
		</KeyWord>
		<KeyWord name="putch" />
		<KeyWord name="putchar" func="yes">
			<Overload retVal="int" >
				<Param name="int c" />
			</Overload>
		</KeyWord>
		<KeyWord name="putenv" />
		<KeyWord name="putimage" />
		<KeyWord name="putpixel" />
		<KeyWord name="puts" func="yes">
			<Overload retVal="int" >
				<Param name="const char *s" />
			</Overload>
		</KeyWord>
		<KeyWord name="puttext" />
		<KeyWord name="putw" />
		<KeyWord name="qsort" func="yes">
			<Overload retVal="void" >
				<Param name="void *base" />
				<Param name="size_t nmemb" />
				<Param name="size_t size" />
			</Overload>
		</KeyWord>
		<KeyWord name="raise" func="yes">
			<Overload retVal="int" >
				<Param name="int sig" />
			</Overload>
		</KeyWord>
		<KeyWord name="rand" func="yes">
			<Overload retVal="int" >
				<Param name="void" />
			</Overload>
		</KeyWord>
		<KeyWord name="randbrd" />
		<KeyWord name="randbwr" />
		<KeyWord name="random" />
		<KeyWord name="randomize" />
		<KeyWord name="read" func="yes">
			<Overload retVal="int" >
				<Param name="int filedes" />
				<Param name="void *buf" />
				<Param name="unsigned int nbyte" />
			</Overload>
		</KeyWord>
		<KeyWord name="readdir" func="yes">
			<Overload retVal="struct dirent *" >
				<Param name="DIR *dirp" />
			</Overload>
		</KeyWord>
		<KeyWord name="real" />
		<KeyWord name="realloc" func="yes">
			<Overload retVal="void" >
				<Param name="void *ptr" />
				<Param name="size_t size" />
			</Overload>
		</KeyWord>
		<KeyWord name="rectangle" />
		<KeyWord name="register" />
		<KeyWord name="registerbgidriver" />
		<KeyWord name="registerbgifont" />
		<KeyWord name="registerfarbgidriver" />
		<KeyWord name="registerfarbgifont" />
		<KeyWord name="remove" func="yes">
			<Overload retVal="int" >
				<Param name="const char *filename" />
			</Overload>
		</KeyWord>
		<KeyWord name="rename" func="yes">
			<Overload retVal="int" >
				<Param name="const char * old" />
				<Param name="const char *new" />
			</Overload>
		</KeyWord>
		<KeyWord name="restorecrtmode" />
		<KeyWord name="return" />
		<KeyWord name="rewind" func="yes">
			<Overload retVal="void" >
				<Param name="FILE *stream" />
			</Overload>
		</KeyWord>
		<KeyWord name="rewinddir" func="yes">
			<Overload retVal="void" >
				<Param name="DIR *dirp" />
			</Overload>
		</KeyWord>
		<KeyWord name="rmdir" func="yes">
			<Overload retVal="int" >
				<Param name="const char *path" />
			</Overload>
		</KeyWord>
		<KeyWord name="rmtmp" />
		<KeyWord name="sbrk" />
		<KeyWord name="scanf" func="yes">
			<Overload retVal="int" >
				<Param name="const char *format" />
				<Param name="..." />
			</Overload>
		</KeyWord>
		<KeyWord name="searchpath" />
		<KeyWord name="sector" />
		<KeyWord name="segread" />
		<KeyWord name="set_new_handler" />
		<KeyWord name="setactivepage" />
		<KeyWord name="setallpalette" />
		<KeyWord name="setaspectratio" />
		<KeyWord name="setbkcolor" />
		<KeyWord name="setblock" />
		<KeyWord name="setbuf" func="yes">
			<Overload retVal="void" >
				<Param name="FILE *stream" />
				<Param name="char *buf" />
			</Overload>
		</KeyWord>
		<KeyWord name="setcbrk" />
		<KeyWord name="setcolor" />
		<KeyWord name="setdate" />
		<KeyWord name="setdisk" />
		<KeyWord name="setdta" />
		<KeyWord name="setfillpattern" />
		<KeyWord name="setfillstyle" />
		<KeyWord name="setftime" />
		<KeyWord name="setgraphbufsize" />
		<KeyWord name="setgraphmode" />
		<KeyWord name="setjmp" func="yes">
			<Overload retVal="int" >
				<Param name="jmp_buf env" />
			</Overload>
		</KeyWord>
		<KeyWord name="setlinestyle" />
		<KeyWord name="setlocale" func="yes">
			<Overload retVal="char *" >
				<Param name="int category" />
				<Param name="const char *locale" />
			</Overload>
		</KeyWord>
		<KeyWord name="setmem" />
		<KeyWord name="setmode" />
		<KeyWord name="setpalette" />
		<KeyWord name="setrgbpalette" />
		<KeyWord name="settextjustify" />
		<KeyWord name="settextstyle" />
		<KeyWord name="settime" />
		<KeyWord name="setusercharsize" />
		<KeyWord name="setvbuf" func="yes">
			<Overload retVal="int" >
				<Param name="FILE *stream" />
				<Param name="char *buf" />
				<Param name="int mode" />
				<Param name="size_t size" />
			</Overload>
		</KeyWord>
		<KeyWord name="setvect" />
		<KeyWord name="setverify" />
		<KeyWord name="setviewport" />
		<KeyWord name="setvisualpage" />
		<KeyWord name="setwritemode" />
		<KeyWord name="short" />
		<KeyWord name="signal" func="yes">
			<Overload retVal="void" >
				<Param name="int sig" />
				<Param name="void *func(int sig)" />
			</Overload>
		</KeyWord>
		<KeyWord name="signed" />
		<KeyWord name="sin" func="yes">
			<Overload retVal="double" >
				<Param name="double x" />
			</Overload>
		</KeyWord>
		<KeyWord name="sinh" func="yes">
			<Overload retVal="double" >
				<Param name="double x" />
			</Overload>
		</KeyWord>
		<KeyWord name="sinhl" />
		<KeyWord name="sinl" />
		<KeyWord name="sizeof" />
		<KeyWord name="sleep" func="yes">
			<Overload retVal="unsigned int" >
				<Param name="unsigned int seconds" />
			</Overload>
		</KeyWord>
		<KeyWord name="sopen" />
		<KeyWord name="sound" />
		<KeyWord name="spawnl" />
		<KeyWord name="spawnle" />
		<KeyWord name="spawnlp" />
		<KeyWord name="spawnlpe" />
		<KeyWord name="spawnv" />
		<KeyWord name="spawnve" />
		<KeyWord name="spawnvp" />
		<KeyWord name="spawnvpe" />
		<KeyWord name="sprintf" func="yes">
			<Overload retVal="int" >
				<Param name="char *s" />
				<Param name="const char *format" />
				<Param name="..." />
			</Overload>
		</KeyWord>
		<KeyWord name="sqrt" func="yes">
			<Overload retVal="double" >
				<Param name="double x" />
			</Overload>
		</KeyWord>
		<KeyWord name="sqrtl" />
		<KeyWord name="srand" func="yes">
			<Overload retVal="void" >
				<Param name="unsigned int seed" />
			</Overload>
		</KeyWord>
		<KeyWord name="sscanf" func="yes">
			<Overload retVal="int" >
				<Param name="const char *s" />
				<Param name="const char *format" />
				<Param name="..." />
			</Overload>
		</KeyWord>
		<KeyWord name="stackavail" />
		<KeyWord name="stat" func="yes">
			<Overload retVal="int" >
				<Param name="const char *path" />
				<Param name="struct stat *buf" />
			</Overload>
		</KeyWord>
		<KeyWord name="static" />
		<KeyWord name="stime" />
		<KeyWord name="stpcpy" />
		<KeyWord name="strcat" func="yes">
			<Overload retVal="char *" >
				<Param name="char *s1" />
				<Param name="const char *s2" />
			</Overload>
		</KeyWord>
		<KeyWord name="strchr" func="yes">
			<Overload retVal="char *" >
				<Param name="const char *s" />
				<Param name="int c" />
			</Overload>
		</KeyWord>
		<KeyWord name="strcmp" func="yes">
			<Overload retVal="int" >
				<Param name="const char *s1" />
				<Param name="const char *s2" />
			</Overload>
		</KeyWord>
		<KeyWord name="strcmpi" />
		<KeyWord name="strcoll" func="yes">
			<Overload retVal="int" >
				<Param name="const char *s1" />
				<Param name="const char *s2" />
			</Overload>
		</KeyWord>
		<KeyWord name="strcpy" func="yes">
			<Overload retVal="char *" >
				<Param name="char *s1" />
				<Param name="const char *s2" />
			</Overload>
		</KeyWord>
		<KeyWord name="strcspn" func="yes">
			<Overload retVal="size_t" >
				<Param name="const char *s1" />
				<Param name="const char *s2" />
			</Overload>
		</KeyWord>
		<KeyWord name="strdup" />
		<KeyWord name="strerror" func="yes">
			<Overload retVal="char *" >
				<Param name="int errnum" />
			</Overload>
		</KeyWord>
		<KeyWord name="strftime" func="yes">
			<Overload retVal="size_t *" >
				<Param name="char *s" />
				<Param name="size_t maxsize" />
				<Param name="const char *format" />
				<Param name="const struct tm *timeptr" />
			</Overload>
		</KeyWord>
		<KeyWord name="stricmp" />
		<KeyWord name="strlen" func="yes">
			<Overload retVal="size_t" >
				<Param name="const char *s" />
			</Overload>
		</KeyWord>
		<KeyWord name="strlwr" />
		<KeyWord name="strncat" func="yes">
			<Overload retVal="char *" >
				<Param name="char *s1" />
				<Param name="const char *s2" />
				<Param name="size_t n" />
			</Overload>
		</KeyWord>
		<KeyWord name="strncmp" func="yes">
			<Overload retVal="int" >
				<Param name="const char *s1" />
				<Param name="const char *s2" />
				<Param name="size_t n" />
			</Overload>
		</KeyWord>
		<KeyWord name="strncmpi" />
		<KeyWord name="strncpy" func="yes">
			<Overload retVal="char *" >
				<Param name="char *s1" />
				<Param name="const char *s2" />
				<Param name="size_t n" />
			</Overload>
		</KeyWord>
		<KeyWord name="strnicmp" />
		<KeyWord name="strnset" />
		<KeyWord name="strpbrk" func="yes">
			<Overload retVal="char *" >
				<Param name="const char *s1" />
				<Param name="const char *s2" />
			</Overload>
		</KeyWord>
		<KeyWord name="strrchr" func="yes">
			<Overload retVal="char *" >
				<Param name="const char *s" />
				<Param name="int c" />
			</Overload>
		</KeyWord>
		<KeyWord name="strrev" />
		<KeyWord name="strset" />
		<KeyWord name="strspn" func="yes">
			<Overload retVal="size_t" >
				<Param name="const char *s1" />
				<Param name="const char *s2" />
			</Overload>
		</KeyWord>
		<KeyWord name="strstr" func="yes">
			<Overload retVal="char *" >
				<Param name="const char *s1" />
				<Param name="const char *s2" />
			</Overload>
		</KeyWord>
		<KeyWord name="strtod" func="yes">
			<Overload retVal="double" >
				<Param name="const char *nptr" />
				<Param name="char **endptr" />
			</Overload>
		</KeyWord>
		<KeyWord name="strtok" func="yes">
			<Overload retVal="char *" >
				<Param name="char *s1" />
				<Param name="const char *s2" />
			</Overload>
		</KeyWord>
		<KeyWord name="strtol" func="yes">
			<Overload retVal="long int" >
				<Param name="const char *nptr" />
				<Param name="char **endptr" />
				<Param name="int base" />
			</Overload>
		</KeyWord>
		<KeyWord name="strtoul" func="yes">
			<Overload retVal="unsigned long int" >
				<Param name="const char *nptr" />
				<Param name="char **endptr" />
				<Param name="int base" />
			</Overload>
		</KeyWord>
		<KeyWord name="struct" />
		<KeyWord name="strupr" />
		<KeyWord name="strxfrm" func="yes">
			<Overload retVal="size_t" >
				<Param name="char *s1" />
				<Param name="const char *s2" />
				<Param name="size_t n" />
			</Overload>
		</KeyWord>
		<KeyWord name="swab" />
		<KeyWord name="switch" />
		<KeyWord name="system" func="yes">
			<Overload retVal="int" >
				<Param name="const char *string" />
			</Overload>
		</KeyWord>
		<KeyWord name="tan" func="yes">
			<Overload retVal="double" >
				<Param name="double x" />
			</Overload>
		</KeyWord>
		<KeyWord name="tanh" func="yes">
			<Overload retVal="double" >
				<Param name="double x" />
			</Overload>
		</KeyWord>
		<KeyWord name="tanhl" />
		<KeyWord name="tanl" />
		<KeyWord name="tell" />
		<KeyWord name="template" />
		<KeyWord name="tempnam" />
		<KeyWord name="textattr" />
		<KeyWord name="textbackground" />
		<KeyWord name="textcolor" />
		<KeyWord name="textheight" />
		<KeyWord name="textmode" />
		<KeyWord name="textwidth" />
		<KeyWord name="thread" />
		<KeyWord name="thread2" />
		<KeyWord name="time" func="yes">
			<Overload retVal="time_t" >
				<Param name="time_t *timer" />
			</Overload>
		</KeyWord>
		<KeyWord name="tmpfile" func="yes">
			<Overload retVal="FILE *" >
				<Param name="void" />
			</Overload>
		</KeyWord>
		<KeyWord name="tmpnam" func="yes">
			<Overload retVal="char *" >
				<Param name="char *s" />
			</Overload>
		</KeyWord>
		<KeyWord name="toascii" />
		<KeyWord name="tolower" func="yes">
			<Overload retVal="int" >
				<Param name="int c" />
			</Overload>
		</KeyWord>
		<KeyWord name="toupper" func="yes">
			<Overload retVal="int" >
				<Param name="int c" />
			</Overload>
		</KeyWord>
		<KeyWord name="try" />
		<KeyWord name="typedef" />
		<KeyWord name="tzset" func="yes">
			<Overload retVal="void" >
				<Param name="void" />
			</Overload>
		</KeyWord>
		<KeyWord name="ultoa" />
		<KeyWord name="umask" func="yes">
			<Overload retVal="mode_t" >
				<Param name="mode_t cmask" />
			</Overload>
		</KeyWord>
		<KeyWord name="ungetc" func="yes">
			<Overload retVal="int" >
				<Param name="int c" />
				<Param name="FILE *stream" />
			</Overload>
		</KeyWord>
		<KeyWord name="ungetch" />
		<KeyWord name="union" />
		<KeyWord name="unixtodos" />
		<KeyWord name="unlink" func="yes">
			<Overload retVal="int" >
				<Param name="const char *path" />
			</Overload>
		</KeyWord>
		<KeyWord name="unlock" />
		<KeyWord name="unsigned" />
		<KeyWord name="using" />
		<KeyWord name="utime" func="yes">
			<Overload retVal="int" >
				<Param name="const char *path" />
				<Param name="const struct utimbuf *times" />
			</Overload>
		</KeyWord>
		<KeyWord name="va_arg" func="yes">
			<Overload retVal="type" >
				<Param name="va_list ap" />
				<Param name="type" />
			</Overload>
		</KeyWord>
		<KeyWord name="va_end" func="yes">
			<Overload retVal="void" >
				<Param name="va_list ap" />
			</Overload>
		</KeyWord>
		<KeyWord name="va_list" />
		<KeyWord name="va_start" func="yes">
			<Overload retVal="void" >
				<Param name="va_list ap" />
				<Param name="parmN" />
			</Overload>
		</KeyWord>
		<KeyWord name="vfprintf" func="yes">
			<Overload retVal="int" >
				<Param name="FILE *stream" />
				<Param name="const char *format" />
				<Param name="va_list arg" />
			</Overload>
		</KeyWord>
		<KeyWord name="vfscanf" />
		<KeyWord name="virtual" />
		<KeyWord name="void" />
		<KeyWord name="volatile" />
		<KeyWord name="vprintf" func="yes">
			<Overload retVal="int" >
				<Param name="const char *format" />
				<Param name="va_list arg" />
			</Overload>
		</KeyWord>
		<KeyWord name="vscanf" />
		<KeyWord name="vsprintf" func="yes">
			<Overload retVal="int" >
				<Param name="char *s" />
				<Param name="const char *format" />
				<Param name="va_list arg" />
			</Overload>
		</KeyWord>
		<KeyWord name="vsscanf" />
		<KeyWord name="wcstombs" func="yes">
			<Overload retVal="size_t" >
				<Param name="char *s" />
				<Param name="const wchar_t *pwcs" />
				<Param name="size_t n" />
			</Overload>
		</KeyWord>
		<KeyWord name="wctomb" func="yes">
			<Overload retVal="int" >
				<Param name="char *s" />
				<Param name="wchar_t wchar" />
			</Overload>
		</KeyWord>
		<KeyWord name="wherex" />
		<KeyWord name="wherey" />
		<KeyWord name="while" />
		<KeyWord name="window" />
		<KeyWord name="write" func="yes">
			<Overload retVal="int" >
				<Param name="int filedes" />
				<Param name="const void *buf" />
				<Param name="unsigned int nbyte" />
			</Overload>
		</KeyWord>
		<KeyWord name="_OvrInitEms" />
		<KeyWord name="_OvrInitExt" />		
		<KeyWord name="__asm" />
		<KeyWord name="__based1" />
		<KeyWord name="__cdecl" />
		<KeyWord name="__declspec" />
		<KeyWord name="__emit__" />
		<KeyWord name="__except" />
		<KeyWord name="__fastcall" />
		<KeyWord name="__finally" />
		<KeyWord name="__inline" />
		<KeyWord name="__int16" />
		<KeyWord name="__int32" />
		<KeyWord name="__int64" />
		<KeyWord name="__int8" />
		<KeyWord name="__leave" />
		<KeyWord name="__multiple_inheritance" />
		<KeyWord name="__single_inheritance" />
		<KeyWord name="__stdcall" />
		<KeyWord name="__try" />
		<KeyWord name="__virtual_inheritance" />
		<KeyWord name="_atold" />
		<KeyWord name="_bios_disk" />
		<KeyWord name="_bios_equiplist" />
		<KeyWord name="_bios_keybrd" />
		<KeyWord name="_bios_memsize" />
		<KeyWord name="_bios_printer" />
		<KeyWord name="_bios_serialcom" />
		<KeyWord name="_bios_timeofday" />
		<KeyWord name="_c_exit" />
		<KeyWord name="_cexit" />
		<KeyWord name="_chain_intr" />
		<KeyWord name="_chdrive" />
		<KeyWord name="_chmod" />
		<KeyWord name="_clear87" />
		<KeyWord name="_close" />
		<KeyWord name="_control87" />
		<KeyWord name="_creat" />
		<KeyWord name="_disable" />
		<KeyWord name="_dos_allocmem" />
		<KeyWord name="_dos_close" />
		<KeyWord name="_dos_creat" />
		<KeyWord name="_dos_creatnew" />
		<KeyWord name="_dos_findfirst" />
		<KeyWord name="_dos_findnext" />
		<KeyWord name="_dos_freemem" />
		<KeyWord name="_dos_getdate" />
		<KeyWord name="_dos_getdiskfree" />
		<KeyWord name="_dos_getdrive" />
		<KeyWord name="_dos_getfileattr" />
		<KeyWord name="_dos_getftime" />
		<KeyWord name="_dos_gettime" />
		<KeyWord name="_dos_getvect" />
		<KeyWord name="_dos_keep" />
		<KeyWord name="_dos_open" />
		<KeyWord name="_dos_read" />
		<KeyWord name="_dos_setblock" />
		<KeyWord name="_dos_setdate" />
		<KeyWord name="_dos_setdrive" />
		<KeyWord name="_dos_setfileattr" />
		<KeyWord name="_dos_setftime" />
		<KeyWord name="_dos_settime" />
		<KeyWord name="_dos_setvect" />
		<KeyWord name="_dos_write" />
		<KeyWord name="_enable" />
		<KeyWord name="_exit" func="yes">
			<Overload retVal="void" >
				<Param name="int status" />
			</Overload>
		</KeyWord>
		<KeyWord name="_fmemccpy" />
		<KeyWord name="_fmemchr" />
		<KeyWord name="_fmemcmp" />
		<KeyWord name="_fmemcpy" />
		<KeyWord name="_fmemicmp" />
		<KeyWord name="_fmemset" />
		<KeyWord name="_fpreset" />
		<KeyWord name="_fsopen" />
		<KeyWord name="_fstrcat" />
		<KeyWord name="_fstrchr" />
		<KeyWord name="_fstrcmp" />
		<KeyWord name="_fstrcpy" />
		<KeyWord name="_fstrcspn" />
		<KeyWord name="_fstrdup" />
		<KeyWord name="_fstricmp" />
		<KeyWord name="_fstrlen" />
		<KeyWord name="_fstrlwr" />
		<KeyWord name="_fstrncat" />
		<KeyWord name="_fstrncmp" />
		<KeyWord name="_fstrncpy" />
		<KeyWord name="_fstrnicmp" />
		<KeyWord name="_fstrnset" />
		<KeyWord name="_fstrpbrk" />
		<KeyWord name="_fstrrchr" />
		<KeyWord name="_fstrrev" />
		<KeyWord name="_fstrset" />
		<KeyWord name="_fstrspn" />
		<KeyWord name="_fstrstr" />
		<KeyWord name="_fstrtok" />
		<KeyWord name="_fstrupr" />
		<KeyWord name="_fullpath" />
		<KeyWord name="_getdcwd" />
		<KeyWord name="_getdrive" />
		<KeyWord name="_graphfreemem" />
		<KeyWord name="_graphgetmem" />
		<KeyWord name="_harderr" />
		<KeyWord name="_hardresume" />
		<KeyWord name="_hardretn" />
		<KeyWord name="_lrotl" />
		<KeyWord name="_lrotr" />
		<KeyWord name="_makepath" />
		<KeyWord name="_matherrl" />
		<KeyWord name="_open" />
		<KeyWord name="_read" />
		<KeyWord name="_rotl" />
		<KeyWord name="_rotr" />
		<KeyWord name="_searchenv" />
		<KeyWord name="_setcursortype" />
		<KeyWord name="_splitpath" />
		<KeyWord name="_status87" />
		<KeyWord name="_strdate" />
		<KeyWord name="_strerror" />
		<KeyWord name="_strtime" />
		<KeyWord name="_strtold" />
		<KeyWord name="_tolower" />
		<KeyWord name="_toupper" />
		<KeyWord name="_write" />
	</AutoComplete>
</NotepadPlus>