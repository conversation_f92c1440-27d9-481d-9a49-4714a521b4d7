@echo off
title Simple Downloader

echo ========================================
echo Portable Apps Downloader
echo ========================================
echo.

echo Creating download directory...
if not exist Downloads mkdir Downloads
cd Downloads

echo.
echo Starting downloads...
echo.

echo [1/3] Downloading 7-Zip...
curl -L -o "7zip.exe" "https://www.7-zip.org/a/7z2408-x64.exe"
if exist "7zip.exe" (echo Success: 7-Zip downloaded) else (echo Failed: 7-Zip download failed)

echo.
echo [2/3] Downloading Notepad++...
curl -L -o "notepadpp.zip" "https://github.com/notepad-plus-plus/notepad-plus-plus/releases/download/v8.5.8/npp.8.5.8.portable.x64.zip"
if exist "notepadpp.zip" (echo Success: Notepad++ downloaded) else (echo Failed: Notepad++ download failed)

echo.
echo [3/3] Downloading Everything...
curl -L -o "everything.zip" "https://www.voidtools.com/Everything-1.4.1.1024.x64.zip"
if exist "everything.zip" (echo Success: Everything downloaded) else (echo Failed: Everything download failed)

echo.
echo ========================================
echo Download Complete!
echo ========================================
echo.
echo Downloaded files:
dir /b
echo.
echo Files saved in Downloads directory
echo Please manually extract and install these apps
echo.
pause
