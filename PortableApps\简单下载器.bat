@echo off
title Enhanced Portable Apps Downloader

echo ========================================
echo Enhanced Portable Apps Downloader
echo ========================================
echo.

echo Creating download directory...
if not exist Downloads mkdir Downloads
cd Downloads

echo.
echo Starting downloads...
echo This may take several minutes depending on your connection...
echo.

echo [1/7] Downloading 7-Zip...
curl -L -o "7zip.exe" "https://www.7-zip.org/a/7z2408-x64.exe"
if exist "7zip.exe" (echo Success: 7-Zip downloaded) else (echo Failed: 7-Zip download failed)

echo.
echo [2/7] Downloading Notepad++...
curl -L -o "notepadpp.zip" "https://github.com/notepad-plus-plus/notepad-plus-plus/releases/download/v8.5.8/npp.8.5.8.portable.x64.zip"
if exist "notepadpp.zip" (echo Success: Notepad++ downloaded) else (echo Failed: Notepad++ download failed)

echo.
echo [3/7] Downloading Everything...
curl -L -o "everything.zip" "https://www.voidtools.com/Everything-1.4.1.1024.x64.zip"
if exist "everything.zip" (echo Success: Everything downloaded) else (echo Failed: Everything download failed)

echo.
echo [4/7] Downloading VS Code Portable...
curl -L -o "vscode.zip" "https://code.visualstudio.com/sha/download?build=stable&os=win32-x64-archive"
if exist "vscode.zip" (echo Success: VS Code downloaded) else (echo Failed: VS Code download failed)

echo.
echo [5/7] Downloading Python Portable...
curl -L -o "python.zip" "https://www.python.org/ftp/python/3.11.9/python-3.11.9-embed-amd64.zip"
if exist "python.zip" (echo Success: Python downloaded) else (echo Failed: Python download failed)

echo.
echo [6/7] Downloading Git Portable...
curl -L -o "git-portable.exe" "https://github.com/git-for-windows/git/releases/download/v2.42.0.windows.2/PortableGit-********-64-bit.7z.exe"
if exist "git-portable.exe" (echo Success: Git Portable downloaded) else (echo Failed: Git Portable download failed)

echo.
echo [7/7] Downloading Process Monitor...
curl -L -o "procmon.zip" "https://download.sysinternals.com/files/ProcessMonitor.zip"
if exist "procmon.zip" (echo Success: Process Monitor downloaded) else (echo Failed: Process Monitor download failed)

echo.
echo ========================================
echo Extracting and organizing apps...
echo ========================================

REM Create target directories
if not exist "..\Editors\NotePadPlusPlus" mkdir "..\Editors\NotePadPlusPlus"
if not exist "..\Editors\VSCode" mkdir "..\Editors\VSCode"
if not exist "..\Development\Python" mkdir "..\Development\Python"
if not exist "..\Development\Git" mkdir "..\Development\Git"
if not exist "..\System" mkdir "..\System"
if not exist "..\Utilities" mkdir "..\Utilities"

REM Extract Notepad++
if exist "notepadpp.zip" (
    echo Extracting Notepad++...
    powershell -Command "Expand-Archive -Path 'notepadpp.zip' -DestinationPath '../Editors/NotePadPlusPlus' -Force"
)

REM Extract VS Code
if exist "vscode.zip" (
    echo Extracting VS Code...
    powershell -Command "Expand-Archive -Path 'vscode.zip' -DestinationPath '../Editors/VSCode' -Force"
)

REM Extract Python
if exist "python.zip" (
    echo Extracting Python...
    powershell -Command "Expand-Archive -Path 'python.zip' -DestinationPath '../Development/Python' -Force"
)

REM Extract Everything
if exist "everything.zip" (
    echo Extracting Everything...
    powershell -Command "Expand-Archive -Path 'everything.zip' -DestinationPath '../Utilities' -Force"
)

REM Extract Process Monitor
if exist "procmon.zip" (
    echo Extracting Process Monitor...
    powershell -Command "Expand-Archive -Path 'procmon.zip' -DestinationPath '../System' -Force"
)

REM Move single file apps
if exist "7zip.exe" (
    echo Moving 7-Zip...
    move "7zip.exe" "..\Utilities\7zip.exe"
)

if exist "git-portable.exe" (
    echo Moving Git Portable...
    move "git-portable.exe" "..\Development\git-portable.exe"
)

echo.
echo ========================================
echo Setup Complete!
echo ========================================
echo.
echo Installed applications:
echo [Editors]
echo   - Notepad++ (Editors/NotePadPlusPlus/)
echo   - VS Code (Editors/VSCode/)
echo.
echo [Development]
echo   - Python 3.11 (Development/Python/)
echo   - Git Portable (Development/git-portable.exe)
echo.
echo [System Tools]
echo   - Process Monitor (System/ProcMon.exe)
echo.
echo [Utilities]
echo   - 7-Zip (Utilities/7zip.exe)
echo   - Everything (Utilities/Everything.exe)
echo.
echo Next steps:
echo 1. Run git-portable.exe to extract Git
echo 2. Configure Python path if needed
echo 3. Launch VS Code from VSCode/Code.exe
echo.
echo Cleaning up downloads...
cd ..
rmdir /s /q Downloads 2>nul

echo.
echo All done! Your portable development environment is ready.
pause
