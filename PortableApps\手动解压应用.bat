@echo off
title Extract Apps

echo ========================================
echo Extract Downloaded Apps
echo ========================================

if not exist "Downloads" (
    echo No Downloads folder found
    echo Please run downloader first
    pause
    exit /b 1
)

cd Downloads

echo Found files:
dir /b

echo Creating directories...
if not exist "..\Editors\NotePadPlusPlus" mkdir "..\Editors\NotePadPlusPlus"
if not exist "..\Editors\VSCode" mkdir "..\Editors\VSCode"
if not exist "..\Development\Python" mkdir "..\Development\Python"
if not exist "..\Development\Git" mkdir "..\Development\Git"
if not exist "..\System" mkdir "..\System"
if not exist "..\Utilities" mkdir "..\Utilities"

echo Extracting apps...

REM Extract Notepad++
if exist "notepadpp.zip" (
    echo [1/3] Extracting Notepad++...
    powershell -Command "Expand-Archive -Path 'notepadpp.zip' -DestinationPath '../Editors/NotePadPlusPlus' -Force"
    echo Done: Notepad++
) else (
    echo Skip: notepadpp.zip not found
)

REM Extract Everything
if exist "everything.zip" (
    echo [2/3] Extracting Everything...
    powershell -Command "Expand-Archive -Path 'everything.zip' -DestinationPath '../Utilities' -Force"
    echo Done: Everything
) else (
    echo Skip: everything.zip not found
)

REM Move 7-Zip
if exist "7zip.exe" (
    echo [3/3] Moving 7-Zip...
    move "7zip.exe" "..\Utilities\7zip.exe"
    echo Done: 7-Zip
) else (
    echo Skip: 7zip.exe not found
)

echo ========================================
echo Extraction Complete
echo ========================================

echo Checking extracted apps:
if exist "..\Editors\NotePadPlusPlus\notepad++.exe" echo   OK: Notepad++
if exist "..\Utilities\Everything.exe" echo   OK: Everything
if exist "..\Utilities\7zip.exe" echo   OK: 7-Zip

echo.
echo Apps are ready to use in sandbox
echo Run startup script to launch sandbox
pause
